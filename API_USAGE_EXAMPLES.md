# 增强的主机查询API使用示例

## 概述

`hostGets` 函数已经增强，现在支持按主机表中的所有字段进行查询。API支持单个值查询和批量查询（多个值用逗号分隔）。

## 支持的查询字段

### 精确匹配字段
这些字段使用精确匹配（IN查询）：
- `id` - 主机ID
- `ip` - IP地址
- `zone` - 可用区
- `idc` - IDC机房
- `gpu` - GPU卡数
- `cpu` - CPU核数
- `mem` - 内存
- `disk` - 磁盘
- `cate` - 类别
- `tenant` - 租户
- `jumpserver_asset_id` - JumpServer资产ID
- `jumpserver_node_id` - JumpServer节点ID
- `current_group` - 当前所在用户组

### 模糊匹配字段
这些字段使用模糊匹配（LIKE查询）：
- `sn` - 序列号
- `ident` - 标识符
- `name` - 主机名
- `os_version` - 操作系统版本
- `kernel_version` - 内核版本
- `cpu_model` - CPU型号
- `gpu_model` - GPU型号
- `rack` - 机架
- `note` - 备注
- `model` - 型号
- `manufacturer` - 厂商

### 范围查询字段
- `clock_min` - 心跳时间戳最小值（Unix时间戳）
- `clock_max` - 心跳时间戳最大值（Unix时间戳）

## API使用示例

### 基础查询

#### 1. 按主机名查询（模糊匹配）
```bash
GET /hosts?name=web-server
GET /hosts?name=web-server,db-server  # 批量查询
```

#### 2. 按IP地址查询（精确匹配）
```bash
GET /hosts?ip=***********
GET /hosts?ip=***********,***********,***********  # 批量查询
```

#### 3. 按主机ID查询（精确匹配）
```bash
GET /hosts?id=1
GET /hosts?id=1,2,3,4,5  # 批量查询
```

### 硬件相关查询

#### 4. 按CPU型号查询（模糊匹配）
```bash
GET /hosts?cpu_model=Intel
GET /hosts?cpu_model=Intel Xeon,AMD EPYC  # 查询Intel Xeon或AMD EPYC
```

#### 5. 按GPU型号查询（模糊匹配）
```bash
GET /hosts?gpu_model=NVIDIA Tesla
GET /hosts?gpu_model=NVIDIA Tesla V100,NVIDIA RTX 3080
```

#### 6. 按CPU核数查询（精确匹配）
```bash
GET /hosts?cpu=8
GET /hosts?cpu=8,16,32  # 查询8核、16核或32核的主机
```

#### 7. 按内存查询（精确匹配）
```bash
GET /hosts?mem=16GB
GET /hosts?mem=16GB,32GB,64GB
```

### 位置和环境查询

#### 8. 按可用区查询（精确匹配）
```bash
GET /hosts?zone=us-west-1a
GET /hosts?zone=us-west-1a,us-west-1b,us-east-1a
```

#### 9. 按IDC机房查询（精确匹配）
```bash
GET /hosts?idc=beijing
GET /hosts?idc=beijing,shanghai,guangzhou
```

#### 10. 按机架查询（模糊匹配）
```bash
GET /hosts?rack=rack-01
GET /hosts?rack=rack-01,rack-02
```

### 系统信息查询

#### 11. 按操作系统版本查询（模糊匹配）
```bash
GET /hosts?os_version=Ubuntu
GET /hosts?os_version=Ubuntu 20.04,CentOS 7
```

#### 12. 按内核版本查询（模糊匹配）
```bash
GET /hosts?kernel_version=5.4
GET /hosts?kernel_version=5.4.0,3.10.0
```

### 设备信息查询

#### 13. 按厂商查询（模糊匹配）
```bash
GET /hosts?manufacturer=Dell
GET /hosts?manufacturer=Dell,HP,Lenovo
```

#### 14. 按型号查询（模糊匹配）
```bash
GET /hosts?model=R740
GET /hosts?model=Dell R740,HP DL380
```

#### 15. 按序列号查询（模糊匹配）
```bash
GET /hosts?sn=SN001
GET /hosts?sn=SN001,SN002,SN003
```

### 管理信息查询

#### 16. 按类别查询（精确匹配）
```bash
GET /hosts?cate=host
GET /hosts?cate=host,vm,container
```

#### 17. 按租户查询（精确匹配）
```bash
GET /hosts?tenant=team-a
GET /hosts?tenant=team-a,team-b,team-c
```

#### 18. 按备注查询（模糊匹配）
```bash
GET /hosts?note=production
GET /hosts?note=production,testing,development
```

### JumpServer集成查询

#### 19. 按JumpServer资产ID查询（精确匹配）
```bash
GET /hosts?jumpserver_asset_id=asset-001
GET /hosts?jumpserver_asset_id=asset-001,asset-002
```

#### 20. 按JumpServer节点ID查询（精确匹配）
```bash
GET /hosts?jumpserver_node_id=node-001
GET /hosts?jumpserver_node_id=node-001,node-002
```

#### 21. 按当前用户组查询（精确匹配）
```bash
GET /hosts?current_group=admin
GET /hosts?current_group=admin,user,guest
```

### 时间范围查询

#### 22. 按心跳时间戳查询
```bash
# 查询最近活跃的主机（心跳时间戳大于指定值）
GET /hosts?clock_min=1640995200

# 查询指定时间范围内活跃的主机
GET /hosts?clock_min=1640995200&clock_max=1672531200

# 查询在指定时间之前活跃的主机
GET /hosts?clock_max=1672531200
```

### 复合查询

#### 23. 多字段组合查询
```bash
# 查询北京机房的Dell服务器，且CPU核数为16或32
GET /hosts?idc=beijing&manufacturer=Dell&cpu=16,32

# 查询生产环境的Ubuntu主机，且内存为32GB或64GB
GET /hosts?note=production&os_version=Ubuntu&mem=32GB,64GB

# 查询指定可用区的虚拟机，且属于特定租户
GET /hosts?zone=us-west-1a,us-west-1b&cate=vm&tenant=team-a,team-b
```

### 分页查询

#### 24. 带分页的查询
```bash
# 第一页，每页20条记录
GET /hosts?name=web&limit=20&p=1

# 第二页，每页50条记录
GET /hosts?cate=host&limit=50&p=2
```

## 响应格式

所有查询都返回相同的响应格式：

```json
{
  "code": 0,
  "dat": {
    "list": [
      {
        "id": 1,
        "sn": "SN001",
        "ip": "***********",
        "ident": "host-001",
        "name": "web-server-01",
        "os_version": "Ubuntu 20.04",
        "kernel_version": "5.4.0-74-generic",
        "cpu_model": "Intel Xeon E5-2680 v4",
        "gpu_model": "NVIDIA Tesla V100",
        "zone": "us-west-1a",
        "rack": "rack-01",
        "note": "production web server",
        "model": "Dell PowerEdge R740",
        "manufacturer": "Dell",
        "idc": "beijing",
        "gpu": "2",
        "cpu": "16",
        "mem": "32GB",
        "disk": "1TB",
        "cate": "host",
        "clock": 1672531200,
        "tenant": "team-a",
        "jumpserver_asset_id": "asset-001",
        "jumpserver_node_id": "node-001",
        "current_group": "admin"
      }
    ],
    "total": 1
  },
  "err": ""
}
```

## 注意事项

1. **查询参数大小写敏感**：所有查询参数都是大小写敏感的
2. **批量查询限制**：建议单次批量查询的值不超过100个，以避免URL过长
3. **模糊匹配**：模糊匹配使用 `%value%` 的方式，会匹配包含指定值的记录
4. **精确匹配**：精确匹配要求完全相等
5. **时间戳格式**：`clock_min` 和 `clock_max` 使用Unix时间戳（秒）
6. **分页参数**：`limit` 默认为20，`p`（页码）默认为1
7. **空值处理**：空字符串和空白字符串会被忽略
