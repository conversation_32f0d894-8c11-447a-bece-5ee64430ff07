logger:
  dir: logs/ams
  level: INFO
  keepHours: 24

http:
  mode: release

i18n:
  lang: zh

# JumpServer配置
jumpserver:
  base_url: "http://10.1.4.213:8090"
  username: "admin"
  password: "password"
  token: "e7261a6a44d0a3991dbd77c9d308be3e887340b6"  # 可以使用token替代用户名密码
  organization: "Default"
  timeout: 30  # 超时时间（秒）

  # 节点配置
  node_config:
    root_node_name: "jump-ams"  # JumpServer中的根节点名称

  # 资产配置
  asset_config:
    default_platform: "Linux"  # 创建资产时的默认平台
    default_port: 22           # 默认SSH端口
    default_protocol: "ssh"    # 默认协议名称

  # 权限配置
  permission_config:
    permission_prefix: "perf-"                              # 权限规则名称前缀
    default_accounts: ["@ALL"]                              # 默认授权账号
    default_protocols: ["all"]                              # 默认授权协议
    default_expired_date: "2095-07-15T02:58:22.634069Z"    # 默认权限过期时间（永不过期）