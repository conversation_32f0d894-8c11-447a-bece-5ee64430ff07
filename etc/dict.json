{"zh": {"cannot convert %s to int64": "%s 无法转为 int64 类型", "cannot convert %s to int": "%s 无法转为 int 类型", "args invalid": "参数无效", "arg[%s] not found": "参数[%s]没找到", "[%s] is blank": "参数[%s]值不能为空", "no such field": "扩展字段未找到", "field_type cannot modify": "字段类型不能被修改", "%s invalid": "%s 不符合规范", "ident is blank": "唯一标识不能为空", "url param[%s] is blank": "url参数[%s]不能为空", "query param[%s] is necessary": "query参数[%s]不能为空", "Cannot find the user by %s": "无法用 %s 找到相关用户", "Invalid arguments %s": "不合法的参数 %s", "IP %s already exists": "IP %s 已存在", "ident %s already exists": "唯一标识已存在", "required field %s(%s) is empty": "%s 字段不能为空", "field %s not defined": "%s 字段未定义", "%s is empty": "%s 为空", "failed to write example row: %v": "写入示例行失败: %v", "failed to open file: %v": "处理文件失败：%v", "failed to parse CSV: %v": "解析文件失败：%v", "CSV file must contain at least one data row": "导入文件至少包含一行数据", "Upper char": "大写字母", "Lower char": "小写字母", "Number": "数字", "Special char": "特殊字符", "Must include %s": "必须包含 %s", "character: %s not supported": "不支持的字符 %s", "no file uploaded": "未选择上传文件", "file too large, maximum size is 10MB": "文件太大，最大支持10MB", "Failed to create stream processor: %v": "解析失败：%v", "EOF": ""}}