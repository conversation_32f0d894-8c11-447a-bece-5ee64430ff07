package models

import (
	"fmt"
	"strings"
	"time"

	"xorm.io/xorm"

	"github.com/toolkits/pkg/logger"
	"github.com/toolkits/pkg/str"
)

type Host struct {
	Id                int64  `json:"id" xorm:"'id' pk autoincr"`
	SN                string `json:"sn" xorm:"'sn'"`
	IP                string `json:"ip" xorm:"'ip'"`
	Ident             string `json:"ident" xorm:"'ident'"`
	Name              string `json:"name" xorm:"'name'"`
	OSVersion         string `json:"os_version" xorm:"'os_version'"`         // 操作系统版本
	KernelVersion     string `json:"kernel_version" xorm:"'kernel_version'"` // 内核版本
	CPUModel          string `json:"cpu_model" xorm:"'cpu_model'"`           // CPU型号
	GPUModel          string `json:"gpu_model" xorm:"'gpu_model'"`           // GPU型号
	Zone              string `json:"zone" xorm:"'zone'"`                     // 可用区
	Rack              string `json:"rack" xorm:"'rack'"`                     // 机架
	Note              string `json:"note" xorm:"'note'"`
	Model             string `json:"model" xorm:"'model'"`
	Manufacturer      string `json:"manufacturer" xorm:"'manufacturer'"`
	IDC               string `json:"idc" xorm:"'idc'"`
	GPU               string `json:"gpu" xorm:"'gpu'"`
	CPU               string `json:"cpu" xorm:"'cpu'"`
	Mem               string `json:"mem" xorm:"'mem'"`
	Disk              string `json:"disk" xorm:"'disk'"`
	Cate              string `json:"cate" xorm:"'cate'"`
	Clock             int64  `json:"clock" xorm:"'clock'"`
	Tenant            string `json:"tenant" xorm:"'tenant'"`
	JumpServerAssetID string `json:"jumpserver_asset_id" xorm:"'jumpserver_asset_id'"` // JumpServer资产ID
	JumpServerNodeID  string `json:"jumpserver_node_id" xorm:"'jumpserver_node_id'"`   // JumpServer节点ID
	CurrentGroup      string `json:"current_group" xorm:"'current_group'"`             // 当前所在用户组
}

// HostQueryParams 主机查询参数结构体
type HostQueryParams struct {
	ID            []string // 主机ID，精确匹配
	SN            []string // 序列号，模糊匹配
	IP            []string // IP地址，精确匹配
	Ident         []string // 标识符，模糊匹配
	Name          []string // 主机名，模糊匹配
	OSVersion     []string // 操作系统版本，模糊匹配
	KernelVersion []string // 内核版本，模糊匹配
	CPUModel      []string // CPU型号，模糊匹配
	GPUModel      []string // GPU型号，模糊匹配
	Zone          []string // 可用区，精确匹配
	Rack          []string // 机架，模糊匹配
	Note          []string // 备注，模糊匹配
	Model         []string // 型号，模糊匹配
	Manufacturer  []string // 厂商，模糊匹配
	IDC           []string // IDC机房，精确匹配
	GPU           []string // GPU卡数，精确匹配
	CPU           []string // CPU核数，精确匹配
	Mem           []string // 内存，精确匹配
	Disk          []string // 磁盘，精确匹配
	Cate          []string // 类别，精确匹配
	Tenant        []string // 租户，精确匹配
}

func (h *Host) Save() error {
	_, err := DB["ams"].Insert(h)
	return err
}

func HostNew(sn, ip, ident, name, cate string, fields map[string]interface{}) (*Host, error) {
	host := new(Host)
	host.SN = sn
	host.IP = ip
	host.Ident = ident
	host.Name = name
	host.Cate = cate
	host.Clock = time.Now().Unix()

	session := DB["ams"].NewSession()
	defer session.Close()

	if err := session.Begin(); err != nil {
		return nil, err
	}

	if _, err := session.Insert(host); err != nil {
		session.Rollback()
		return nil, err
	}

	if len(fields) > 0 {
		if _, err := session.Table(new(Host)).ID(host.Id).Update(fields); err != nil {
			session.Rollback()
			return nil, err
		}
	}

	err := session.Commit()
	if err != nil {
		return nil, err
	}

	// 重新获取完整的主机信息
	updatedHost, err := HostGet("id=?", host.Id)
	if err != nil {
		return nil, err
	}

	return updatedHost, nil
}

// HostBatchNew 批量创建主机，提高导入性能
func HostBatchNew(hosts []*Host) error {
	if len(hosts) == 0 {
		return nil
	}

	session := DB["ams"].NewSession()
	defer session.Close()

	if err := session.Begin(); err != nil {
		return err
	}

	// 设置时间戳
	now := time.Now().Unix()
	for _, host := range hosts {
		if host.Clock == 0 {
			host.Clock = now
		}
	}

	// 批量插入主机
	affected, err := session.Insert(hosts)
	if err != nil {
		session.Rollback()
		return err
	}

	if err := session.Commit(); err != nil {
		return err
	}

	// 对于某些数据库驱动，批量插入后ID可能没有自动设置
	// 如果ID为0，尝试通过IP和Ident重新查询获取ID
	for _, host := range hosts {
		if host.Id == 0 {
			existingHost, err := HostGet("ip=? AND ident=?", host.IP, host.Ident)
			if err == nil && existingHost != nil {
				host.Id = existingHost.Id
				host.Clock = existingHost.Clock
			}
		}
	}

	logger.Debugf("HostBatchNew: inserted %d hosts, affected rows: %d", len(hosts), affected)
	return nil
}

// HostCheckDuplicates 批量检查主机IP和Ident重复
func HostCheckDuplicates(ips, idents []string) (map[string]bool, error) {
	if len(ips) == 0 && len(idents) == 0 {
		return make(map[string]bool), nil
	}

	duplicateMap := make(map[string]bool)

	// 使用UNION查询一次性检查IP和Ident，减少数据库往返
	if len(ips) > 0 && len(idents) > 0 {
		// 构建UNION查询
		var results []struct {
			Value string `xorm:"value"`
		}

		// 使用原生SQL进行优化查询
		sql := "SELECT ip as value FROM host WHERE ip IN (?" + strings.Repeat(",?", len(ips)-1) + ") " +
			"UNION " +
			"SELECT ident as value FROM host WHERE ident IN (?" + strings.Repeat(",?", len(idents)-1) + ")"

		args := make([]interface{}, 0, len(ips)+len(idents))
		for _, ip := range ips {
			args = append(args, ip)
		}
		for _, ident := range idents {
			args = append(args, ident)
		}

		err := DB["ams"].SQL(sql, args...).Find(&results)
		if err != nil {
			return nil, err
		}

		for _, result := range results {
			duplicateMap[result.Value] = true
		}
	} else {
		// 单独处理IP或Ident的情况
		if len(ips) > 0 {
			var existingIPs []string
			err := DB["ams"].Table("host").In("ip", ips).Cols("ip").Find(&existingIPs)
			if err != nil {
				return nil, err
			}
			for _, ip := range existingIPs {
				duplicateMap[ip] = true
			}
		}

		if len(idents) > 0 {
			var existingIdents []string
			err := DB["ams"].Table("host").In("ident", idents).Cols("ident").Find(&existingIdents)
			if err != nil {
				return nil, err
			}
			for _, ident := range existingIdents {
				duplicateMap[ident] = true
			}
		}
	}

	return duplicateMap, nil
}

// HostCheckDuplicatesWithNames 批量检查主机IP、Ident和Name重复
func HostCheckDuplicatesWithNames(ips, idents, names []string) (map[string]bool, error) {
	duplicateMap := make(map[string]bool)

	if len(ips) == 0 && len(idents) == 0 && len(names) == 0 {
		return duplicateMap, nil
	}

	// 构建查询条件
	var conditions []string
	var args []interface{}

	// 检查IP重复
	if len(ips) > 0 {
		ipPlaceholders := strings.Repeat("?,", len(ips))
		ipPlaceholders = ipPlaceholders[:len(ipPlaceholders)-1] // 移除最后的逗号
		conditions = append(conditions, fmt.Sprintf("SELECT ip as value FROM host WHERE ip IN (%s)", ipPlaceholders))
		for _, ip := range ips {
			args = append(args, ip)
		}
	}

	// 检查Ident重复
	if len(idents) > 0 {
		identPlaceholders := strings.Repeat("?,", len(idents))
		identPlaceholders = identPlaceholders[:len(identPlaceholders)-1]
		conditions = append(conditions, fmt.Sprintf("SELECT ident as value FROM host WHERE ident IN (%s)", identPlaceholders))
		for _, ident := range idents {
			args = append(args, ident)
		}
	}

	// 检查Name重复（只检查非空的name）
	if len(names) > 0 {
		nonEmptyNames := make([]string, 0, len(names))
		for _, name := range names {
			if name != "" {
				nonEmptyNames = append(nonEmptyNames, name)
			}
		}

		if len(nonEmptyNames) > 0 {
			namePlaceholders := strings.Repeat("?,", len(nonEmptyNames))
			namePlaceholders = namePlaceholders[:len(namePlaceholders)-1]
			conditions = append(conditions, fmt.Sprintf("SELECT name as value FROM host WHERE name IN (%s)", namePlaceholders))
			for _, name := range nonEmptyNames {
				args = append(args, name)
			}
		}
	}

	if len(conditions) == 0 {
		return duplicateMap, nil
	}

	// 执行UNION查询
	sql := strings.Join(conditions, " UNION ")
	var results []struct {
		Value string `xorm:"value"`
	}

	err := DB["ams"].SQL(sql, args...).Find(&results)
	if err != nil {
		return nil, err
	}

	// 构建重复映射
	for _, result := range results {
		duplicateMap[result.Value] = true
	}

	return duplicateMap, nil
}

func (h *Host) Update(fields map[string]interface{}) error {
	_, err := DB["ams"].Table(new(Host)).ID(h.Id).Update(fields)
	return err
}

func (h *Host) Del() error {
	session := DB["ams"].NewSession()
	defer session.Close()

	if err := session.Begin(); err != nil {
		return err
	}

	// 先删除主机的自定义字段值
	if _, err := session.Where("host_id = ?", h.Id).Delete(new(HostFieldValue)); err != nil {
		session.Rollback()
		return err
	}

	// 再删除主机本身
	if _, err := session.Where("id = ?", h.Id).Delete(new(Host)); err != nil {
		session.Rollback()
		return err
	}

	return session.Commit()
}

func HostUpdateNote(ids []int64, note string) error {
	_, err := DB["ams"].Exec("UPDATE host SET note=? WHERE id in ("+str.IdsString(ids)+")", note)
	return err
}

func HostUpdateCate(ids []int64, cate string) error {
	_, err := DB["ams"].Exec("UPDATE host SET cate=? WHERE id in ("+str.IdsString(ids)+")", cate)
	return err
}

func HostUpdateTenant(ids []int64, tenant string) error {
	_, err := DB["ams"].Exec("UPDATE host SET tenant=? WHERE id in ("+str.IdsString(ids)+")", tenant)
	return err
}

func HostGet(where string, args ...interface{}) (*Host, error) {
	var obj Host
	has, err := DB["ams"].Where(where, args...).Get(&obj)
	if err != nil {
		return nil, err
	}

	if !has {
		return nil, nil
	}

	return &obj, nil
}

func HostGets(where string, args ...interface{}) (hosts []Host, err error) {
	if where != "" {
		err = DB["ams"].Where(where, args...).Find(&hosts)
	} else {
		err = DB["ams"].Find(&hosts)
	}
	return hosts, err
}

func HostByIds(ids []int64) (hosts []Host, err error) {
	if len(ids) == 0 {
		return
	}

	err = DB["ams"].In("id", ids).Find(&hosts)
	return
}

func HostIdsByIps(ips []string) (ids []int64, err error) {
	err = DB["ams"].Table(new(Host)).In("ip", ips).Select("id").Find(&ids)
	return
}

// HostGetByJumpServerAssetID 根据JumpServer资产ID获取主机
func HostGetByJumpServerAssetID(assetID string) (*Host, error) {
	return HostGet("jumpserver_asset_id=?", assetID)
}

// HostUpdateJumpServerInfo 更新主机的JumpServer信息
func HostUpdateJumpServerInfo(hostID int64, assetID, nodeID, group string) error {
	fields := map[string]interface{}{
		"jumpserver_asset_id": assetID,
		"jumpserver_node_id":  nodeID,
		"current_group":       group,
	}
	_, err := DB["ams"].Table(new(Host)).ID(hostID).Update(fields)
	return err
}

func HostTotalForAdmin(tenant, query, batch, field string) (int64, error) {
	return buildHostWhere(tenant, query, batch, field).Count()
}

func HostGetsForAdmin(tenant, query, batch, field string, limit, offset int) ([]Host, error) {
	var objs []Host
	err := buildHostWhere(tenant, query, batch, field).Limit(limit, offset).Find(&objs)
	return objs, err
}

func buildHostWhere(tenant, query, batch, field string) *xorm.Session {
	session := DB["ams"].Table(new(Host)).OrderBy("ident")

	if tenant == "0" {
		session = session.Where("tenant=?", "")
	} else if tenant != "" {
		session = session.Where("tenant=?", tenant)
	}

	if batch == "" && query != "" {
		arr := strings.Fields(query)
		for i := 0; i < len(arr); i++ {
			q := "%" + arr[i] + "%"
			session = session.Where("cate=? or sn=? or ident like ? or ip like ? or name like ? or note like ? or model like ? or idc like ? or manufacturer like ? or os_version like ? or kernel_version like ? or cpu_model like ? or gpu_model like ? or zone like ? or rack like ?",
				arr[i], arr[i], q, q, q, q, q, q, q, q, q, q, q, q, q)
		}
	}

	if batch != "" {
		arr := str.ParseLines(strings.Replace(batch, ",", "\n", -1))
		if len(arr) > 0 {
			session = session.In(field, arr)
		}
	}

	return session
}

// HostTotalForCustomField 获取自定义字段过滤后的总数
func HostTotalForCustomField(tenant, query, batch, fieldIdent string) (int64, error) {
	session := DB["ams"].Table("host_field_value").
		Join("INNER", "host", "host.id = host_field_value.host_id").
		Where("host_field_value.field_ident = ?", fieldIdent)

	if tenant == "0" {
		session = session.Where("host.tenant = ?", "")
	} else if tenant != "" {
		session = session.Where("host.tenant = ?", tenant)
	}

	if batch != "" {
		// 批量精确匹配
		values := str.ParseLines(strings.Replace(batch, ",", "\n", -1))
		if len(values) > 0 {
			session = session.In("host_field_value.field_value", values)
		}
	} else if query != "" {
		// 单值模糊匹配
		session = session.Where("host_field_value.field_value LIKE ?", "%"+query+"%")
	}

	return session.Count()
}

// HostGetsForCustomField 获取自定义字段过滤后的主机列表
func HostGetsForCustomField(tenant, query, batch, fieldIdent string, limit, offset int) ([]Host, error) {
	session := DB["ams"].Table("host").
		Join("INNER", "host_field_value", "host.id = host_field_value.host_id").
		Where("host_field_value.field_ident = ?", fieldIdent)

	if tenant == "0" {
		session = session.Where("host.tenant = ?", "")
	} else if tenant != "" {
		session = session.Where("host.tenant = ?", tenant)
	}

	if batch != "" {
		// 批量精确匹配
		values := str.ParseLines(strings.Replace(batch, ",", "\n", -1))
		if len(values) > 0 {
			session = session.In("host_field_value.field_value", values)
		}
	} else if query != "" {
		// 单值模糊匹配
		session = session.Where("host_field_value.field_value LIKE ?", "%"+query+"%")
	}

	var hosts []Host
	err := session.OrderBy("host.ident").Limit(limit, offset).Find(&hosts)
	return hosts, err
}

// HostTotalByFields 根据name、ip、zone、idc字段查询主机总数
func HostTotalByFields(names, ips, zones, idcs []string) (int64, error) {
	session := DB["ams"].Table(new(Host))

	session = buildFieldsWhere(session, names, ips, zones, idcs)

	return session.Count()
}

// HostGetsByFields 根据name、ip、zone、idc字段查询主机列表
func HostGetsByFields(names, ips, zones, idcs []string, limit, offset int) ([]Host, error) {
	session := DB["ams"].Table(new(Host))

	session = buildFieldsWhere(session, names, ips, zones, idcs)

	var hosts []Host
	err := session.OrderBy("ident").Limit(limit, offset).Find(&hosts)
	return hosts, err
}

// buildFieldsWhere 构建name、ip、zone、idc字段的查询条件
func buildFieldsWhere(session *xorm.Session, names, ips, zones, idcs []string) *xorm.Session {
	hasCondition := false

	// name字段查询 - 模糊匹配
	if len(names) > 0 {
		if len(names) == 1 {
			session = session.Where("name LIKE ?", "%"+names[0]+"%")
		} else {
			// 多个name值，使用OR条件
			conditions := make([]string, len(names))
			args := make([]interface{}, len(names))
			for i, name := range names {
				conditions[i] = "name LIKE ?"
				args[i] = "%" + name + "%"
			}
			session = session.Where("("+strings.Join(conditions, " OR ")+")", args...)
		}
		hasCondition = true
	}

	// ip字段查询 - 精确匹配
	if len(ips) > 0 {
		session = session.In("ip", ips)
		hasCondition = true
	}

	// zone字段查询 - 精确匹配
	if len(zones) > 0 {
		session = session.In("zone", zones)
		hasCondition = true
	}

	// idc字段查询 - 精确匹配
	if len(idcs) > 0 {
		session = session.In("idc", idcs)
		hasCondition = true
	}

	// 如果没有任何查询条件，返回所有记录
	if !hasCondition {
		// 不添加额外条件，返回所有记录
	}

	return session
}

// HostGetsAll 获取所有主机记录
func HostGetsAll() ([]*Host, error) {
	var hosts []*Host
	err := DB["ams"].Find(&hosts)
	return hosts, err
}

// HostTotalByAllFields 根据所有字段查询主机总数
func HostTotalByAllFields(params HostQueryParams) (int64, error) {
	session := DB["ams"].Table(new(Host))
	session = buildAllFieldsWhere(session, params)
	return session.Count()
}

// HostGetsByAllFields 根据所有字段查询主机列表
func HostGetsByAllFields(params HostQueryParams, limit, offset int) ([]Host, error) {
	session := DB["ams"].Table(new(Host))
	session = buildAllFieldsWhere(session, params)

	var hosts []Host
	err := session.OrderBy("ident").Limit(limit, offset).Find(&hosts)
	return hosts, err
}

// buildAllFieldsWhere 构建所有字段的查询条件
func buildAllFieldsWhere(session *xorm.Session, params HostQueryParams) *xorm.Session {
	// ID字段查询 - 精确匹配
	if len(params.ID) > 0 {
		session = session.In("id", params.ID)
	}

	// SN字段查询 - 模糊匹配
	if len(params.SN) > 0 {
		if len(params.SN) == 1 {
			session = session.Where("sn LIKE ?", "%"+params.SN[0]+"%")
		} else {
			conditions := make([]string, len(params.SN))
			args := make([]interface{}, len(params.SN))
			for i, sn := range params.SN {
				conditions[i] = "sn LIKE ?"
				args[i] = "%" + sn + "%"
			}
			session = session.Where("("+strings.Join(conditions, " OR ")+")", args...)
		}
	}

	// IP字段查询 - 精确匹配
	if len(params.IP) > 0 {
		session = session.In("ip", params.IP)
	}

	// Ident字段查询 - 模糊匹配
	if len(params.Ident) > 0 {
		if len(params.Ident) == 1 {
			session = session.Where("ident LIKE ?", "%"+params.Ident[0]+"%")
		} else {
			conditions := make([]string, len(params.Ident))
			args := make([]interface{}, len(params.Ident))
			for i, ident := range params.Ident {
				conditions[i] = "ident LIKE ?"
				args[i] = "%" + ident + "%"
			}
			session = session.Where("("+strings.Join(conditions, " OR ")+")", args...)
		}
	}

	// Name字段查询 - 模糊匹配
	if len(params.Name) > 0 {
		if len(params.Name) == 1 {
			session = session.Where("name LIKE ?", "%"+params.Name[0]+"%")
		} else {
			conditions := make([]string, len(params.Name))
			args := make([]interface{}, len(params.Name))
			for i, name := range params.Name {
				conditions[i] = "name LIKE ?"
				args[i] = "%" + name + "%"
			}
			session = session.Where("("+strings.Join(conditions, " OR ")+")", args...)
		}
	}

	// OSVersion字段查询 - 模糊匹配
	if len(params.OSVersion) > 0 {
		if len(params.OSVersion) == 1 {
			session = session.Where("os_version LIKE ?", "%"+params.OSVersion[0]+"%")
		} else {
			conditions := make([]string, len(params.OSVersion))
			args := make([]interface{}, len(params.OSVersion))
			for i, osVersion := range params.OSVersion {
				conditions[i] = "os_version LIKE ?"
				args[i] = "%" + osVersion + "%"
			}
			session = session.Where("("+strings.Join(conditions, " OR ")+")", args...)
		}
	}

	// KernelVersion字段查询 - 模糊匹配
	if len(params.KernelVersion) > 0 {
		if len(params.KernelVersion) == 1 {
			session = session.Where("kernel_version LIKE ?", "%"+params.KernelVersion[0]+"%")
		} else {
			conditions := make([]string, len(params.KernelVersion))
			args := make([]interface{}, len(params.KernelVersion))
			for i, kernelVersion := range params.KernelVersion {
				conditions[i] = "kernel_version LIKE ?"
				args[i] = "%" + kernelVersion + "%"
			}
			session = session.Where("("+strings.Join(conditions, " OR ")+")", args...)
		}
	}

	// CPUModel字段查询 - 模糊匹配
	if len(params.CPUModel) > 0 {
		if len(params.CPUModel) == 1 {
			session = session.Where("cpu_model LIKE ?", "%"+params.CPUModel[0]+"%")
		} else {
			conditions := make([]string, len(params.CPUModel))
			args := make([]interface{}, len(params.CPUModel))
			for i, cpuModel := range params.CPUModel {
				conditions[i] = "cpu_model LIKE ?"
				args[i] = "%" + cpuModel + "%"
			}
			session = session.Where("("+strings.Join(conditions, " OR ")+")", args...)
		}
	}

	// GPUModel字段查询 - 模糊匹配
	if len(params.GPUModel) > 0 {
		if len(params.GPUModel) == 1 {
			session = session.Where("gpu_model LIKE ?", "%"+params.GPUModel[0]+"%")
		} else {
			conditions := make([]string, len(params.GPUModel))
			args := make([]interface{}, len(params.GPUModel))
			for i, gpuModel := range params.GPUModel {
				conditions[i] = "gpu_model LIKE ?"
				args[i] = "%" + gpuModel + "%"
			}
			session = session.Where("("+strings.Join(conditions, " OR ")+")", args...)
		}
	}

	// Zone字段查询 - 精确匹配
	if len(params.Zone) > 0 {
		session = session.In("zone", params.Zone)
	}

	// Rack字段查询 - 模糊匹配
	if len(params.Rack) > 0 {
		if len(params.Rack) == 1 {
			session = session.Where("rack LIKE ?", "%"+params.Rack[0]+"%")
		} else {
			conditions := make([]string, len(params.Rack))
			args := make([]interface{}, len(params.Rack))
			for i, rack := range params.Rack {
				conditions[i] = "rack LIKE ?"
				args[i] = "%" + rack + "%"
			}
			session = session.Where("("+strings.Join(conditions, " OR ")+")", args...)
		}
	}

	// Note字段查询 - 模糊匹配
	if len(params.Note) > 0 {
		if len(params.Note) == 1 {
			session = session.Where("note LIKE ?", "%"+params.Note[0]+"%")
		} else {
			conditions := make([]string, len(params.Note))
			args := make([]interface{}, len(params.Note))
			for i, note := range params.Note {
				conditions[i] = "note LIKE ?"
				args[i] = "%" + note + "%"
			}
			session = session.Where("("+strings.Join(conditions, " OR ")+")", args...)
		}
	}

	// Model字段查询 - 模糊匹配
	if len(params.Model) > 0 {
		if len(params.Model) == 1 {
			session = session.Where("model LIKE ?", "%"+params.Model[0]+"%")
		} else {
			conditions := make([]string, len(params.Model))
			args := make([]interface{}, len(params.Model))
			for i, model := range params.Model {
				conditions[i] = "model LIKE ?"
				args[i] = "%" + model + "%"
			}
			session = session.Where("("+strings.Join(conditions, " OR ")+")", args...)
		}
	}

	// Manufacturer字段查询 - 模糊匹配
	if len(params.Manufacturer) > 0 {
		if len(params.Manufacturer) == 1 {
			session = session.Where("manufacturer LIKE ?", "%"+params.Manufacturer[0]+"%")
		} else {
			conditions := make([]string, len(params.Manufacturer))
			args := make([]interface{}, len(params.Manufacturer))
			for i, manufacturer := range params.Manufacturer {
				conditions[i] = "manufacturer LIKE ?"
				args[i] = "%" + manufacturer + "%"
			}
			session = session.Where("("+strings.Join(conditions, " OR ")+")", args...)
		}
	}

	// IDC字段查询 - 精确匹配
	if len(params.IDC) > 0 {
		session = session.In("idc", params.IDC)
	}

	// GPU字段查询 - 精确匹配
	if len(params.GPU) > 0 {
		session = session.In("gpu", params.GPU)
	}

	// CPU字段查询 - 精确匹配
	if len(params.CPU) > 0 {
		session = session.In("cpu", params.CPU)
	}

	// Mem字段查询 - 精确匹配
	if len(params.Mem) > 0 {
		session = session.In("mem", params.Mem)
	}

	// Disk字段查询 - 精确匹配
	if len(params.Disk) > 0 {
		session = session.In("disk", params.Disk)
	}

	// Cate字段查询 - 精确匹配
	if len(params.Cate) > 0 {
		session = session.In("cate", params.Cate)
	}

	// Tenant字段查询 - 精确匹配
	if len(params.Tenant) > 0 {
		session = session.In("tenant", params.Tenant)
	}

	return session
}
