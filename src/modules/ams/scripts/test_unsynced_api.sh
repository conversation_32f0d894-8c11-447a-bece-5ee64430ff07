#!/bin/bash

# AMS未同步主机API测试脚本
# 使用方法: ./test_unsynced_api.sh [BASE_URL] [API_TOKEN]

BASE_URL=${1:-"http://localhost:8080"}
API_TOKEN=${2:-"your-api-token-here"}

echo "AMS 未同步主机API测试"
echo "===================="
echo "Base URL: $BASE_URL"
echo "API Token: ${API_TOKEN:0:10}..."
echo ""

# 测试1: 基本查询 - 获取所有未同步主机
echo "测试1: 获取所有未同步主机（默认分页）"
echo "----------------------------------------"
curl -s -H "X-User-Token: $API_TOKEN" \
     -H "Content-Type: application/json" \
     "$BASE_URL/api/ams-ce/hosts/unsynced" | jq '.'
echo ""

# 测试2: 分页查询
echo "测试2: 分页查询（每页5条，第1页）"
echo "--------------------------------"
curl -s -H "X-User-Token: $API_TOKEN" \
     -H "Content-Type: application/json" \
     "$BASE_URL/api/ams-ce/hosts/unsynced?limit=5&p=1" | jq '.'
echo ""

# 测试3: 按类别过滤
echo "测试3: 按类别过滤（host类型）"
echo "----------------------------"
curl -s -H "X-User-Token: $API_TOKEN" \
     -H "Content-Type: application/json" \
     "$BASE_URL/api/ams-ce/hosts/unsynced?cate=host" | jq '.'
echo ""

# 测试4: 按IDC过滤
echo "测试4: 按IDC过滤（beijing机房）"
echo "------------------------------"
curl -s -H "X-User-Token: $API_TOKEN" \
     -H "Content-Type: application/json" \
     "$BASE_URL/api/ams-ce/hosts/unsynced?idc=beijing" | jq '.'
echo ""

# 测试5: 多字段组合过滤
echo "测试5: 多字段组合过滤（host类型 + beijing机房）"
echo "--------------------------------------------"
curl -s -H "X-User-Token: $API_TOKEN" \
     -H "Content-Type: application/json" \
     "$BASE_URL/api/ams-ce/hosts/unsynced?cate=host&idc=beijing&limit=10" | jq '.'
echo ""

# 测试6: 批量查询（多个类别）
echo "测试6: 批量查询（host,vm类型）"
echo "-----------------------------"
curl -s -H "X-User-Token: $API_TOKEN" \
     -H "Content-Type: application/json" \
     "$BASE_URL/api/ams-ce/hosts/unsynced?cate=host,vm" | jq '.'
echo ""

# 测试7: 按厂商过滤
echo "测试7: 按厂商过滤（Dell）"
echo "------------------------"
curl -s -H "X-User-Token: $API_TOKEN" \
     -H "Content-Type: application/json" \
     "$BASE_URL/api/ams-ce/hosts/unsynced?manufacturer=Dell" | jq '.'
echo ""

# 测试8: 错误测试 - 无效Token
echo "测试8: 错误测试 - 无效Token"
echo "--------------------------"
curl -s -H "X-User-Token: invalid-token" \
     -H "Content-Type: application/json" \
     "$BASE_URL/api/ams-ce/hosts/unsynced" | jq '.'
echo ""

# 测试9: 性能测试 - 大分页
echo "测试9: 性能测试 - 大分页（每页100条）"
echo "-----------------------------------"
time curl -s -H "X-User-Token: $API_TOKEN" \
          -H "Content-Type: application/json" \
          "$BASE_URL/api/ams-ce/hosts/unsynced?limit=100" | jq '.dat.total'
echo ""

echo "测试完成！"
echo ""
echo "使用说明："
echo "1. 确保AMS服务正在运行"
echo "2. 替换正确的API Token"
echo "3. 根据需要调整BASE_URL"
echo "4. 安装jq工具以获得更好的JSON格式化输出"
