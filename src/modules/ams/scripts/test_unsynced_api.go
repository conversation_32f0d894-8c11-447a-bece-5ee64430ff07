package main

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"strings"
	"time"
)

// API响应结构
type ApiResponse struct {
	Code int         `json:"code"`
	Dat  interface{} `json:"dat"`
	Err  string      `json:"err"`
}

// 未同步主机响应结构
type UnsyncedHostsResponse struct {
	List  []interface{} `json:"list"`
	Total int           `json:"total"`
}

func main() {
	// 从环境变量或命令行参数获取配置
	baseURL := getEnvOrDefault("AMS_BASE_URL", "http://localhost:8080")
	apiToken := getEnvOrDefault("AMS_API_TOKEN", "your-api-token-here")

	if len(os.Args) > 1 {
		baseURL = os.Args[1]
	}
	if len(os.Args) > 2 {
		apiToken = os.Args[2]
	}

	fmt.Println("AMS 未同步主机API测试")
	fmt.Println("==================")
	fmt.Printf("Base URL: %s\n", baseURL)
	fmt.Printf("API Token: %s...\n", apiToken[:min(10, len(apiToken))])
	fmt.Println()

	client := &http.Client{Timeout: 30 * time.Second}

	// 测试用例
	testCases := []struct {
		name        string
		endpoint    string
		description string
	}{
		{
			name:        "基本查询",
			endpoint:    "/api/ams-ce/hosts/unsynced",
			description: "获取所有未同步主机（默认分页）",
		},
		{
			name:        "分页查询",
			endpoint:    "/api/ams-ce/hosts/unsynced?limit=5&p=1",
			description: "分页查询（每页5条，第1页）",
		},
		{
			name:        "按类别过滤",
			endpoint:    "/api/ams-ce/hosts/unsynced?cate=host",
			description: "按类别过滤（host类型）",
		},
		{
			name:        "按IDC过滤",
			endpoint:    "/api/ams-ce/hosts/unsynced?idc=beijing",
			description: "按IDC过滤（beijing机房）",
		},
		{
			name:        "多字段组合过滤",
			endpoint:    "/api/ams-ce/hosts/unsynced?cate=host&idc=beijing&limit=10",
			description: "多字段组合过滤（host类型 + beijing机房）",
		},
		{
			name:        "批量查询",
			endpoint:    "/api/ams-ce/hosts/unsynced?cate=host,vm",
			description: "批量查询（host,vm类型）",
		},
		{
			name:        "按厂商过滤",
			endpoint:    "/api/ams-ce/hosts/unsynced?manufacturer=Dell",
			description: "按厂商过滤（Dell）",
		},
	}

	successCount := 0
	totalTests := len(testCases)

	for i, tc := range testCases {
		fmt.Printf("测试 %d/%d: %s\n", i+1, totalTests, tc.name)
		fmt.Printf("描述: %s\n", tc.description)
		fmt.Printf("URL: %s%s\n", baseURL, tc.endpoint)

		success := runTest(client, baseURL+tc.endpoint, apiToken)
		if success {
			successCount++
			fmt.Println("✅ 测试通过")
		} else {
			fmt.Println("❌ 测试失败")
		}
		fmt.Println(strings.Repeat("-", 50))
	}

	// 错误测试
	fmt.Println("错误测试: 无效Token")
	fmt.Printf("URL: %s/api/ams-ce/hosts/unsynced\n", baseURL)
	runErrorTest(client, baseURL+"/api/ams-ce/hosts/unsynced", "invalid-token")
	fmt.Println(strings.Repeat("-", 50))

	// 总结
	fmt.Printf("\n测试总结:\n")
	fmt.Printf("总测试数: %d\n", totalTests)
	fmt.Printf("成功: %d\n", successCount)
	fmt.Printf("失败: %d\n", totalTests-successCount)
	fmt.Printf("成功率: %.1f%%\n", float64(successCount)/float64(totalTests)*100)

	if successCount == totalTests {
		fmt.Println("🎉 所有测试通过！")
	} else {
		fmt.Println("⚠️  部分测试失败，请检查配置和服务状态")
	}
}

func runTest(client *http.Client, url, token string) bool {
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		fmt.Printf("创建请求失败: %v\n", err)
		return false
	}

	req.Header.Set("X-User-Token", token)
	req.Header.Set("Content-Type", "application/json")

	start := time.Now()
	resp, err := client.Do(req)
	duration := time.Since(start)

	if err != nil {
		fmt.Printf("请求失败: %v\n", err)
		return false
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("读取响应失败: %v\n", err)
		return false
	}

	var apiResp ApiResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		fmt.Printf("解析JSON失败: %v\n", err)
		fmt.Printf("原始响应: %s\n", string(body))
		return false
	}

	fmt.Printf("响应时间: %v\n", duration)
	fmt.Printf("HTTP状态码: %d\n", resp.StatusCode)
	fmt.Printf("API状态码: %d\n", apiResp.Code)

	if apiResp.Code == 0 {
		// 尝试解析数据部分
		if datMap, ok := apiResp.Dat.(map[string]interface{}); ok {
			if total, exists := datMap["total"]; exists {
				fmt.Printf("未同步主机总数: %v\n", total)
			}
			if list, exists := datMap["list"]; exists {
				if listArray, ok := list.([]interface{}); ok {
					fmt.Printf("返回主机数量: %d\n", len(listArray))
				}
			}
		}
		return true
	} else {
		fmt.Printf("API错误: %s\n", apiResp.Err)
		return false
	}
}

func runErrorTest(client *http.Client, url, token string) {
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		fmt.Printf("创建请求失败: %v\n", err)
		return
	}

	req.Header.Set("X-User-Token", token)
	req.Header.Set("Content-Type", "application/json")

	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("请求失败: %v\n", err)
		return
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("读取响应失败: %v\n", err)
		return
	}

	var apiResp ApiResponse
	if err := json.Unmarshal(body, &apiResp); err != nil {
		fmt.Printf("解析JSON失败: %v\n", err)
		return
	}

	fmt.Printf("HTTP状态码: %d\n", resp.StatusCode)
	fmt.Printf("API状态码: %d\n", apiResp.Code)

	if apiResp.Code != 0 {
		fmt.Println("✅ 错误测试通过 - 正确返回了错误")
		fmt.Printf("错误信息: %s\n", apiResp.Err)
	} else {
		fmt.Println("❌ 错误测试失败 - 应该返回错误但返回了成功")
	}
}

func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
