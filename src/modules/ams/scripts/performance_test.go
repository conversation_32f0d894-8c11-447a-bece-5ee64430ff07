package main

import (
	"fmt"
	"log"
	"time"

	"arboris/src/modules/ams/service"
)

// 性能测试脚本，用于验证全局实例优化的效果
func main() {
	fmt.Println("AMS JumpServerService Performance Test")
	fmt.Println("=====================================")

	// 测试1: 传统方式 - 每次创建新实例
	fmt.Println("\n1. Testing traditional approach (NewJumpServerService)...")
	testTraditionalApproach()

	// 测试2: 优化方式 - 使用全局实例
	fmt.Println("\n2. Testing optimized approach (GetJumpServerServiceWithFallback)...")
	testOptimizedApproach()

	// 测试3: 性能对比
	fmt.Println("\n3. Performance comparison...")
	performanceComparison()

	// 测试4: 显示统计信息
	fmt.Println("\n4. Global SyncManager statistics...")
	showStatistics()
}

func testTraditionalApproach() {
	start := time.Now()
	iterations := 5

	for i := 0; i < iterations; i++ {
		jsService, err := service.NewJumpServerService()
		if err != nil {
			log.Printf("Iteration %d failed: %v", i+1, err)
			continue
		}
		fmt.Printf("Iteration %d: Created JumpServerService instance\n", i+1)
		_ = jsService // 使用实例避免编译器警告
	}

	duration := time.Since(start)
	fmt.Printf("Traditional approach: %d iterations took %v (avg: %v per iteration)\n", 
		iterations, duration, duration/time.Duration(iterations))
}

func testOptimizedApproach() {
	// 首先初始化全局实例
	if err := service.InitGlobalSyncManager(); err != nil {
		log.Printf("Failed to initialize global SyncManager: %v", err)
		return
	}

	start := time.Now()
	iterations := 5

	for i := 0; i < iterations; i++ {
		jsService, err := service.GetJumpServerServiceWithFallback()
		if err != nil {
			log.Printf("Iteration %d failed: %v", i+1, err)
			continue
		}
		fmt.Printf("Iteration %d: Got JumpServerService from global instance\n", i+1)
		_ = jsService // 使用实例避免编译器警告
	}

	duration := time.Since(start)
	fmt.Printf("Optimized approach: %d iterations took %v (avg: %v per iteration)\n", 
		iterations, duration, duration/time.Duration(iterations))
}

func performanceComparison() {
	iterations := 10

	// 测试传统方式
	fmt.Printf("Running %d iterations with traditional approach...\n", iterations)
	start1 := time.Now()
	for i := 0; i < iterations; i++ {
		_, err := service.NewJumpServerService()
		if err != nil {
			log.Printf("Traditional iteration %d failed: %v", i+1, err)
		}
	}
	traditional := time.Since(start1)

	// 确保全局实例已初始化
	service.InitGlobalSyncManager()

	// 测试优化方式
	fmt.Printf("Running %d iterations with optimized approach...\n", iterations)
	start2 := time.Now()
	for i := 0; i < iterations; i++ {
		_, err := service.GetJumpServerServiceWithFallback()
		if err != nil {
			log.Printf("Optimized iteration %d failed: %v", i+1, err)
		}
	}
	optimized := time.Since(start2)

	// 计算性能提升
	improvement := float64(traditional-optimized) / float64(traditional) * 100
	speedup := float64(traditional) / float64(optimized)

	fmt.Printf("\nPerformance Comparison Results:\n")
	fmt.Printf("Traditional approach: %v (%v per iteration)\n", traditional, traditional/time.Duration(iterations))
	fmt.Printf("Optimized approach:   %v (%v per iteration)\n", optimized, optimized/time.Duration(iterations))
	fmt.Printf("Performance improvement: %.2f%%\n", improvement)
	fmt.Printf("Speed up: %.2fx faster\n", speedup)
}

func showStatistics() {
	stats := service.GetGlobalSyncManagerStats()
	
	fmt.Printf("Global SyncManager Statistics:\n")
	for key, value := range stats {
		fmt.Printf("  %s: %v\n", key, value)
	}

	// 健康检查
	if healthy, ok := stats["healthy"].(bool); ok && healthy {
		fmt.Println("✅ Global SyncManager is healthy")
	} else {
		fmt.Println("❌ Global SyncManager is not healthy")
	}

	// 性能检查
	if ratio, ok := stats["performance_ratio"].(float64); ok {
		if ratio > 0.8 {
			fmt.Printf("✅ Good performance ratio: %.2f%%\n", ratio*100)
		} else {
			fmt.Printf("⚠️  Low performance ratio: %.2f%% (consider checking initialization)\n", ratio*100)
		}
	}
}
