package jumpserver

// PhoneInfo 电话信息结构
type PhoneInfo struct {
	Code  string `json:"code"`
	Phone int64  `json:"phone"`
}

// User JumpServer用户结构
type User struct {
	ID       string     `json:"id"`
	Username string     `json:"username"`
	Name     string     `json:"name"`
	Email    string     `json:"email"`
	IsActive bool       `json:"is_active"`
	Phone    *PhoneInfo `json:"phone"` // 可能为null，所以使用指针
}

// UserGroup JumpServer用户组结构
type UserGroup struct {
	ID      string `json:"id"`
	Name    string `json:"name"`
	Comment string `json:"comment"`
}

// Node JumpServer节点结构
type Node struct {
	ID       string `json:"id"`
	Key      string `json:"key"`
	Value    string `json:"value"`
	Parent   string `json:"parent"`
	FullPath string `json:"full_path"`
}

// Asset JumpServer资产结构
type Asset struct {
	ID        string            `json:"id"`
	Hostname  string            `json:"hostname"`
	IP        string            `json:"ip"`
	Platform  string            `json:"platform"`
	Protocols []Protocol        `json:"protocols"`
	IsActive  bool              `json:"is_active"`
	Comment   string            `json:"comment"`
	Labels    map[string]string `json:"labels"`
	Nodes     []string          `json:"nodes"`
}

// Protocol 协议配置
type Protocol struct {
	Name string `json:"name"`
	Port int    `json:"port"`
}

// Permission JumpServer权限结构
type Permission struct {
	ID          string                   `json:"id"`
	Name        string                   `json:"name"`
	Users       []string                 `json:"users"`
	UserGroups  []string                 `json:"user_groups"`
	Assets      []string                 `json:"assets"`
	Nodes       []string                 `json:"nodes"`
	Accounts    []string                 `json:"accounts"`
	Protocols   []string                 `json:"protocols"`
	Actions     []map[string]interface{} `json:"actions"`
	IsActive    bool                     `json:"is_active"`
	DateStart   string                   `json:"date_start"`
	DateExpired string                   `json:"date_expired"`
}

// APIResponse JumpServer API响应
type APIResponse struct {
	Count    int         `json:"count"`
	Next     string      `json:"next"`
	Previous string      `json:"previous"`
	Results  interface{} `json:"results"`
}

type Nodes struct {
	Id        string `json:"id"`
	Key       string `json:"key"`
	Value     string `json:"value"`
	OrgId     string `json:"org_id"`
	Name      string `json:"name"`
	FullValue string `json:"full_value"`
	OrgName   string `json:"org_name"`
}

// ErrorResponse 错误响应
type ErrorResponse struct {
	Detail string            `json:"detail"`
	Errors map[string]string `json:"errors"`
}

// FieldErrorResponse JumpServer字段级错误响应
type FieldErrorResponse map[string][]string

// UserCreateRequest 用户创建请求
type UserCreateRequest struct {
	Username string `json:"username"`
	Name     string `json:"name"`
	Email    string `json:"email"`
	Phone    string `json:"phone"`
	IsActive bool   `json:"is_active"`
	Password string `json:"password,omitempty"` // 可选密码字段
}

// UserCreateResponse 用户创建响应（包含密码）
type UserCreateResponse struct {
	User     *User  `json:"user"`
	Password string `json:"password"`
}

// PermissionCreateRequest 权限创建请求
type PermissionCreateRequest struct {
	Name        string   `json:"name"`
	UserGroups  []string `json:"user_groups"`
	Assets      []string `json:"assets"`
	Nodes       []string `json:"nodes"`
	Protocols   []string `json:"protocols"`
	IsActive    bool     `json:"is_active"`
	DateExpired string   `json:"date_expired"`
}

// UserAsset 用户资产查询API返回的结构
type UserAsset struct {
	ID           string      `json:"id"`
	Name         string      `json:"name"`
	Address      string      `json:"address"`
	Domain       interface{} `json:"domain"`
	Platform     struct {
		ID   int    `json:"id"`
		Name string `json:"name"`
	} `json:"platform"`
	OrgID        string `json:"org_id"`
	Connectivity struct {
		Value string `json:"value"`
		Label string `json:"label"`
	} `json:"connectivity"`
	Nodes []struct {
		ID   string `json:"id"`
		Name string `json:"name"`
	} `json:"nodes"`
	Labels   []interface{} `json:"labels"`
	Category struct {
		Value string `json:"value"`
		Label string `json:"label"`
	} `json:"category"`
	Type struct {
		Value string `json:"value"`
		Label string `json:"label"`
	} `json:"type"`
	OrgName      string      `json:"org_name"`
	IsActive     bool        `json:"is_active"`
	DateVerified interface{} `json:"date_verified"`
	DateCreated  string      `json:"date_created"`
	Comment      string      `json:"comment"`
	CreatedBy    string      `json:"created_by"`
}
