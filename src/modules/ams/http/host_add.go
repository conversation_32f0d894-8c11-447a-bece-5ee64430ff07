package http

import (
	"arboris/src/models"
	"arboris/src/modules/ams/service"
	"github.com/gin-gonic/gin"
	"github.com/toolkits/pkg/logger"
)

type hostAddForm struct {
	SN            string            `json:"sn"`
	IP            string            `json:"ip" binding:"required"`
	Ident         string            `json:"ident" binding:"required"`
	Name          string            `json:"name"`
	Cate          string            `json:"cate"`
	Note          string            `json:"note"`
	Model         string            `json:"model"`
	Manufacturer  string            `json:"manufacturer"`
	IDC           string            `json:"idc"`
	GPU           string            `json:"gpu"`
	CPU           string            `json:"cpu"`
	Mem           string            `json:"mem"`
	Disk          string            `json:"disk"`
	Tenant        string            `json:"tenant"`
	OSVersion     string            `json:"os_version"`     // 操作系统版本
	KernelVersion string            `json:"kernel_version"` // 内核版本
	CPUModel      string            `json:"cpu_model"`      // CPU型号
	GPUModel      string            `json:"gpu_model"`      // GPU型号
	Zone          string            `json:"zone"`           // 可用区
	Rack          string            `json:"rack"`           // 机架
	Fields        map[string]string `json:"fields"`         // 自定义字段
}

// @Summary 添加主机
// @Description 添加单台主机，支持基础字段和自定义字段。注意：IP、标识符(ident)和主机名(name)必须唯一，与JumpServer保持一致
// @Tags 主机管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param host body hostAddForm true "主机信息"
// @Success 200 {object} ApiResponse{dat=models.Host} "添加成功"
// @Failure 400 {object} ApiResponse "请求参数错误（包括IP、ident或name重复）"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /host [post]
func hostAdd(c *gin.Context) {
	var f hostAddForm
	bind(c, &f)

	// 检查IP和Ident是否已存在
	exists, err := models.HostGet("ip=? or ident=?", f.IP, f.Ident)
	dangerous(err)

	if exists != nil {
		if exists.IP == f.IP {
			bomb("IP %s already exists", f.IP)
		}
		if exists.Ident == f.Ident {
			bomb("ident %s already exists", f.Ident)
		}
	}

	if f.Name != "" {
		nameExists, err := models.HostGet("name=?", f.Name)
		dangerous(err)

		if nameExists != nil {
			bomb("name %s already exists", f.Name)
		}
	}

	// 获取所有可用的自定义字段定义
	hostFields, err := models.HostFieldGets()
	dangerous(err)

	// 创建字段映射，用于验证
	fieldMap := make(map[string]models.HostField)
	for _, field := range hostFields {
		fieldMap[field.FieldIdent] = field
	}

	// 验证必填的自定义字段
	for _, field := range hostFields {
		if field.FieldRequired == 1 {
			if value, exists := f.Fields[field.FieldIdent]; !exists || value == "" {
				bomb("required field %s(%s) is empty", field.FieldName, field.FieldIdent)
			}
		}
	}

	// 验证提供的自定义字段是否都是合法的
	for fieldIdent := range f.Fields {
		if _, exists := fieldMap[fieldIdent]; !exists {
			bomb("field %s not defined", fieldIdent)
		}
	}

	// 准备基本字段
	fields := make(map[string]interface{})
	if f.CPU != "" {
		fields["cpu"] = f.CPU
	}
	if f.Mem != "" {
		fields["mem"] = f.Mem
	}
	if f.Disk != "" {
		fields["disk"] = f.Disk
	}
	if f.GPU != "" {
		fields["gpu"] = f.GPU
	}
	if f.Model != "" {
		fields["model"] = f.Model
	}
	if f.IDC != "" {
		fields["idc"] = f.IDC
	}
	if f.Manufacturer != "" {
		fields["manufacturer"] = f.Manufacturer
	}
	if f.Note != "" {
		fields["note"] = f.Note
	}
	// 处理新增字段
	if f.OSVersion != "" {
		fields["os_version"] = f.OSVersion
	}
	if f.KernelVersion != "" {
		fields["kernel_version"] = f.KernelVersion
	}
	if f.CPUModel != "" {
		fields["cpu_model"] = f.CPUModel
	}
	if f.GPUModel != "" {
		fields["gpu_model"] = f.GPUModel
	}
	if f.Zone != "" {
		fields["zone"] = f.Zone
	}
	if f.Rack != "" {
		fields["rack"] = f.Rack
	}

	// 创建主机
	host, err := models.HostNew(f.SN, f.IP, f.Ident, f.Name, f.Cate, fields)
	dangerous(err)

	// 如果指定了租户，更新租户信息
	if f.Tenant != "" {
		err = models.HostUpdateTenant([]int64{host.Id}, f.Tenant)
		dangerous(err)
	}

	// 添加自定义字段值
	if len(f.Fields) > 0 {
		fieldValues := make([]models.HostFieldValue, 0, len(f.Fields))
		for fieldIdent, value := range f.Fields {
			if value != "" {
				fieldValues = append(fieldValues, models.HostFieldValue{
					HostId:     host.Id,
					FieldIdent: fieldIdent,
					FieldValue: value,
				})
			}
		}

		if len(fieldValues) > 0 {
			err = models.HostFieldValuePuts(host.Id, fieldValues)
			dangerous(err)
		}
	}

	syncManager, err := service.NewSyncManager()
	if err != nil {
		logger.Errorf("Failed to create sync manager: %v", err)
	} else {
		if result, err := syncManager.CreateHostWithSync(host); err != nil {
			logger.Errorf("Failed to sync host %d: %v", host.Id, err)
		} else if result != nil && !result.Success {
			logger.Errorf("Host %d sync failed: %s", host.Id, result.Error)
		}
	}

	renderData(c, host, nil)
}
