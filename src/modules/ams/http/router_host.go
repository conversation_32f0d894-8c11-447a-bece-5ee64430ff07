package http

import (
	"arboris/src/models"
	"arboris/src/modules/ams/service"
	"fmt"
	"github.com/toolkits/pkg/logger"
	"strings"

	"github.com/gin-gonic/gin"
)

// @Summary 获取主机列表
// @Description 获取主机列表，支持分页和多字段查询。支持单个查询和批量查询（多个值用逗号分隔）
// @Tags 主机管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param limit query int false "每页数量" default(20)
// @Param p query int false "页码" default(1)
// @Param id query string false "主机ID查询，支持批量查询，多个值用逗号分隔" example("1,2,3")
// @Param sn query string false "序列号查询，支持批量查询，多个值用逗号分隔" example("SN001,SN002")
// @Param ip query string false "IP地址查询，支持批量查询，多个值用逗号分隔" example("***********,***********")
// @Param ident query string false "标识符查询，支持批量查询，多个值用逗号分隔" example("host-001,host-002")
// @Param name query string false "主机名查询，支持批量查询，多个值用逗号分隔" example("web-server,db-server")
// @Param os_version query string false "操作系统版本查询，支持批量查询，多个值用逗号分隔" example("Ubuntu 20.04,CentOS 7")
// @Param kernel_version query string false "内核版本查询，支持批量查询，多个值用逗号分隔" example("5.4.0,3.10.0")
// @Param cpu_model query string false "CPU型号查询，支持批量查询，多个值用逗号分隔" example("Intel Xeon,AMD EPYC")
// @Param gpu_model query string false "GPU型号查询，支持批量查询，多个值用逗号分隔" example("NVIDIA Tesla V100,NVIDIA RTX 3080")
// @Param zone query string false "可用区查询，支持批量查询，多个值用逗号分隔" example("us-west-1a,us-west-1b")
// @Param rack query string false "机架查询，支持批量查询，多个值用逗号分隔" example("rack-01,rack-02")
// @Param note query string false "备注查询，支持批量查询，多个值用逗号分隔" example("production,testing")
// @Param model query string false "型号查询，支持批量查询，多个值用逗号分隔" example("Dell R740,HP DL380")
// @Param manufacturer query string false "厂商查询，支持批量查询，多个值用逗号分隔" example("Dell,HP,Lenovo")
// @Param idc query string false "IDC机房查询，支持批量查询，多个值用逗号分隔" example("beijing,shanghai")
// @Param gpu query string false "GPU卡数查询，支持批量查询，多个值用逗号分隔" example("1,2,4")
// @Param cpu query string false "CPU核数查询，支持批量查询，多个值用逗号分隔" example("8,16,32")
// @Param mem query string false "内存查询，支持批量查询，多个值用逗号分隔" example("16GB,32GB,64GB")
// @Param disk query string false "磁盘查询，支持批量查询，多个值用逗号分隔" example("500GB,1TB,2TB")
// @Param cate query string false "类别查询，支持批量查询，多个值用逗号分隔" example("host,vm,container")
// @Param tenant query string false "租户查询，支持批量查询，多个值用逗号分隔" example("team-a,team-b")
// @Success 200 {object} ApiResponse{dat=HostListResponse} "主机列表"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /hosts [get]
func hostGets(c *gin.Context) {
	limit := queryInt(c, "limit", 20)

	// 获取所有查询参数
	params := models.HostQueryParams{
		ID:            parseStringSlice(queryStr(c, "id", "")),
		SN:            parseStringSlice(queryStr(c, "sn", "")),
		IP:            parseStringSlice(queryStr(c, "ip", "")),
		Ident:         parseStringSlice(queryStr(c, "ident", "")),
		Name:          parseStringSlice(queryStr(c, "name", "")),
		OSVersion:     parseStringSlice(queryStr(c, "os_version", "")),
		KernelVersion: parseStringSlice(queryStr(c, "kernel_version", "")),
		CPUModel:      parseStringSlice(queryStr(c, "cpu_model", "")),
		GPUModel:      parseStringSlice(queryStr(c, "gpu_model", "")),
		Zone:          parseStringSlice(queryStr(c, "zone", "")),
		Rack:          parseStringSlice(queryStr(c, "rack", "")),
		Note:          parseStringSlice(queryStr(c, "note", "")),
		Model:         parseStringSlice(queryStr(c, "model", "")),
		Manufacturer:  parseStringSlice(queryStr(c, "manufacturer", "")),
		IDC:           parseStringSlice(queryStr(c, "idc", "")),
		GPU:           parseStringSlice(queryStr(c, "gpu", "")),
		CPU:           parseStringSlice(queryStr(c, "cpu", "")),
		Mem:           parseStringSlice(queryStr(c, "mem", "")),
		Disk:          parseStringSlice(queryStr(c, "disk", "")),
		Cate:          parseStringSlice(queryStr(c, "cate", "")),
		Tenant:        parseStringSlice(queryStr(c, "tenant", "")),
	}

	total, err := models.HostTotalByAllFields(params)
	dangerous(err)

	list, err := models.HostGetsByAllFields(params, limit, offset(c, limit))
	dangerous(err)

	renderData(c, gin.H{
		"list":  list,
		"total": total,
	}, nil)
}

// parseStringSlice 解析逗号分隔的字符串为字符串切片
func parseStringSlice(param string) []string {
	if param == "" {
		return nil
	}

	values := strings.Split(param, ",")
	result := make([]string, 0, len(values))
	for _, value := range values {
		trimmed := strings.TrimSpace(value)
		if trimmed != "" {
			result = append(result, trimmed)
		}
	}

	if len(result) == 0 {
		return nil
	}
	return result
}

// @Summary 获取单个主机信息
// @Description 根据主机ID获取主机详细信息
// @Tags 主机管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "主机ID"
// @Success 200 {object} ApiResponse{dat=models.Host} "主机信息"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 404 {object} ApiResponse "主机不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /host/{id} [get]
func hostGet(c *gin.Context) {
	host, err := models.HostGet("id=?", urlParamInt64(c, "id"))
	renderData(c, host, err)
}

type idsOrIpsForm struct {
	Ids []int64  `json:"ids"`
	Ips []string `json:"ips"`
}

func (f *idsOrIpsForm) Validate() {
	if len(f.Ids) == 0 {
		if len(f.Ips) == 0 {
			bomb("args invalid")
		}
		ids, err := models.HostIdsByIps(f.Ips)
		dangerous(err)

		f.Ids = ids
	}
}

type hostNodeForm struct {
	Ids    []int64 `json:"ids"`
	NodeId int64   `json:"nodeid"`
}

func (f *hostNodeForm) Validate() {
	if len(f.Ids) == 0 {
		bomb("%s is empty", "ids")
	}

	if f.NodeId == 0 {
		bomb("nodeid is blank")
	}

	if f.NodeId < 0 {
		bomb("nodeid is illegal")
	}
}

type hostNoteForm struct {
	Ids  []int64 `json:"ids"`
	Note string  `json:"note"`
}

// @Summary 批量修改主机备注
// @Description 批量修改主机设备的备注信息
// @Tags 主机管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param note body hostNoteForm true "备注信息"
// @Success 200 {object} ApiResponse "修改成功"
// @Failure 400 {object} ApiResponse "请求参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /hosts/note [put]
func hostNotePut(c *gin.Context) {
	var f hostNoteForm
	bind(c, &f)

	if len(f.Ids) == 0 {
		bomb("%s is empty", "ids")
	}

	//loginUser(c).CheckPermGlobal("ams_host_modify")

	renderMessage(c, models.HostUpdateNote(f.Ids, f.Note))
}

type hostCateForm struct {
	Ids  []int64 `json:"ids"`
	Cate string  `json:"cate"`
}

// @Summary 批量修改主机类别
// @Description 批量修改主机设备的类别信息
// @Tags 主机管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param cate body hostCateForm true "类别信息"
// @Success 200 {object} ApiResponse "修改成功"
// @Failure 400 {object} ApiResponse "请求参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /hosts/cate [put]
func hostCatePut(c *gin.Context) {
	var f hostCateForm
	bind(c, &f)

	if len(f.Ids) == 0 {
		bomb("%s is empty", "ids")
	}

	//loginUser(c).CheckPermGlobal("ams_host_modify")

	renderMessage(c, models.HostUpdateCate(f.Ids, f.Cate))
}

type hostUpdateForm struct {
	SN            *string `json:"sn,omitempty"`
	Name          *string `json:"name,omitempty"`
	OSVersion     *string `json:"os_version,omitempty"`
	KernelVersion *string `json:"kernel_version,omitempty"`
	CPUModel      *string `json:"cpu_model,omitempty"`
	GPUModel      *string `json:"gpu_model,omitempty"`
	Zone          *string `json:"zone,omitempty"`
	Rack          *string `json:"rack,omitempty"`
	Note          *string `json:"note,omitempty"`
	Model         *string `json:"model,omitempty"`
	Manufacturer  *string `json:"manufacturer,omitempty"`
	IDC           *string `json:"idc,omitempty"`
	GPU           *string `json:"gpu,omitempty"`
	CPU           *string `json:"cpu,omitempty"`
	Mem           *string `json:"mem,omitempty"`
	Disk          *string `json:"disk,omitempty"`
	Cate          *string `json:"cate,omitempty"`
	Tenant        *string `json:"tenant,omitempty"`
}

// @Summary 更新主机信息
// @Description 更新单台主机的详细信息，支持基础字段。IP、ID和标识符不可修改，主机名(name)必须唯一
// @Tags 主机管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param id path int true "主机ID"
// @Param body body hostUpdateForm true "主机更新参数"
// @Success 200 {object} ApiResponse{dat=models.Host} "更新后的主机信息"
// @Failure 400 {object} ApiResponse "参数错误（包括name重复）"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 404 {object} ApiResponse "主机不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /host/{id} [put]
func hostUpdate(c *gin.Context) {
	hostId := urlParamInt64(c, "id")

	var f hostUpdateForm
	bind(c, &f)

	// 获取现有主机信息
	host, err := models.HostGet("id=?", hostId)
	dangerous(err)

	if host == nil {
		bomb("host not found")
	}

	if f.Name != nil && *f.Name != "" && *f.Name != host.Name {
		nameExists, err := models.HostGet("name=? AND id!=?", *f.Name, hostId)
		dangerous(err)

		if nameExists != nil {
			bomb("name %s already exists", *f.Name)
		}
	}

	updateFields := make(map[string]interface{})

	if f.SN != nil {
		updateFields["sn"] = *f.SN
	}
	if f.Name != nil {
		updateFields["name"] = *f.Name
	}
	if f.OSVersion != nil {
		updateFields["os_version"] = *f.OSVersion
	}
	if f.KernelVersion != nil {
		updateFields["kernel_version"] = *f.KernelVersion
	}
	if f.CPUModel != nil {
		updateFields["cpu_model"] = *f.CPUModel
	}
	if f.GPUModel != nil {
		updateFields["gpu_model"] = *f.GPUModel
	}
	if f.Zone != nil {
		updateFields["zone"] = *f.Zone
	}
	if f.Rack != nil {
		updateFields["rack"] = *f.Rack
	}
	if f.Note != nil {
		updateFields["note"] = *f.Note
	}
	if f.Model != nil {
		updateFields["model"] = *f.Model
	}
	if f.Manufacturer != nil {
		updateFields["manufacturer"] = *f.Manufacturer
	}
	if f.IDC != nil {
		updateFields["idc"] = *f.IDC
	}
	if f.GPU != nil {
		updateFields["gpu"] = *f.GPU
	}
	if f.CPU != nil {
		updateFields["cpu"] = *f.CPU
	}
	if f.Mem != nil {
		updateFields["mem"] = *f.Mem
	}
	if f.Disk != nil {
		updateFields["disk"] = *f.Disk
	}
	if f.Cate != nil {
		updateFields["cate"] = *f.Cate
	}
	if f.Tenant != nil {
		updateFields["tenant"] = *f.Tenant
	}

	// 更新基础字段
	if len(updateFields) > 0 {
		err = host.Update(updateFields)
		dangerous(err)
	}

	// 重新获取更新后的主机信息
	updatedHost, err := models.HostGet("id=?", hostId)
	dangerous(err)

	syncManager, err := service.GetSyncManagerWithFallback()
	if err != nil {
		logger.Errorf("Failed to get sync manager: %v", err)
	} else {
		if result, err := syncManager.UpdateHostWithSync(updatedHost); err != nil {
			logger.Errorf("Failed to update host %d in JumpServer: %v", updatedHost.Id, err)
		} else if result != nil && !result.Success {
			logger.Errorf("Host %d update failed: %s", updatedHost.Id, result.Error)
		}
	}

	renderData(c, updatedHost, nil)
}

// @Summary 批量删除主机
// @Description 批量删除主机设备，支持通过ID或IP地址删除
// @Tags 主机管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param hosts body idsOrIpsForm true "主机ID或IP列表"
// @Success 200 {object} ApiResponse "删除成功"
// @Failure 400 {object} ApiResponse "请求参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /hosts [delete]
func hostDel(c *gin.Context) {
	var f idsOrIpsForm
	bind(c, &f)
	f.Validate()

	//loginUser(c).CheckPermGlobal("ams_host_delete")

	count := len(f.Ids)
	for i := 0; i < count; i++ {
		id := f.Ids[i]

		host, err := models.HostGet("id=?", id)
		dangerous(err)

		if host == nil {
			continue
		}

		// 使用同步管理器进行一致性删除
		syncManager, err := service.GetSyncManagerWithFallback()
		if err != nil {
			logger.Errorf("Failed to get sync manager: %v", err)
			// 如果同步管理器获取失败，仍然删除AMS记录
			dangerous(host.Del())
		} else {
			if result, err := syncManager.DeleteHostWithSync(host); err != nil {
				// 如果JumpServer删除失败，不删除AMS记录，保持一致性
				logger.Errorf("Failed to delete host %d from JumpServer: %v", host.Id, err)
				bomb(fmt.Sprintf("Failed to delete host from JumpServer: %v", err))
			} else if result != nil && !result.Success {
				logger.Errorf("Host %d deletion failed: %s", host.Id, result.Error)
				bomb(fmt.Sprintf("Failed to delete host: %s", result.Error))
			} else {
				// JumpServer删除成功，删除AMS记录
				dangerous(host.Del())
			}
		}
	}

	renderMessage(c, nil)
}
