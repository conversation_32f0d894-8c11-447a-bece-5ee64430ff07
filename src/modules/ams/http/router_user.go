package http

import (
	"fmt"
	"net/http"
	"strings"

	"arboris/src/models"
	"arboris/src/modules/ams/service"
	"github.com/gin-gonic/gin"
	"github.com/toolkits/pkg/logger"
)

// UserCreateRequest 用户创建请求
type UserCreateRequest struct {
	Username    string `json:"username" binding:"required"`
	DisplayName string `json:"display_name" binding:"required"`
	Email       string `json:"email" binding:"required"`
	Phone       string `json:"phone"`
}

// UserCreateResponse 用户创建响应
type UserCreateResponse struct {
	ID       string `json:"id"`
	Username string `json:"username"`
	Name     string `json:"name"`
	Email    string `json:"email"`
	IsActive bool   `json:"is_active"`
	Password string `json:"password"` // 添加密码字段
}

// @Summary 创建用户
// @Description 创建用户并同步到JumpServer，自动生成随机密码
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param user body UserCreateRequest true "用户信息"
// @Success 200 {object} ApiResponse{dat=UserCreateResponse} "创建成功，返回用户信息和生成的密码"
// @Failure 400 {object} ApiResponse "请求参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /users [post]
func userCreate(c *gin.Context) {
	var req UserCreateRequest
	bind(c, &req)

	jsService, err := service.GetJumpServerServiceWithFallback()
	if err != nil {
		logger.Errorf("[API] userCreate failed to get JumpServer service: %v", err)
		dangerous(err)
	}

	// 创建用户并生成随机密码
	userResp, err := jsService.CreateUserWithPassword(req.Username, req.DisplayName, req.Email, req.Phone)
	if err != nil {
		logger.Errorf("[API] userCreate failed to create user %s: %v", req.Username, err)
		dangerous(err)
	}

	response := UserCreateResponse{
		ID:       userResp.User.ID,
		Username: userResp.User.Username,
		Name:     userResp.User.Name,
		Email:    userResp.User.Email,
		IsActive: userResp.User.IsActive,
		Password: userResp.Password, // 返回生成的密码
	}

	renderData(c, response, nil)
}

// @Summary 删除用户
// @Description 删除用户（从JumpServer中删除）
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param username path string true "用户名"
// @Success 200 {object} ApiResponse "删除成功"
// @Failure 400 {object} ApiResponse "请求参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 404 {object} ApiResponse "用户不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /users/{username} [delete]
func userDelete(c *gin.Context) {
	username := c.Param("username")
	if username == "" {
		bomb("username is required")
	}

	jsService, err := service.GetJumpServerServiceWithFallback()
	dangerous(err)

	// 删除用户
	err = jsService.DeleteUser(username)
	dangerous(err)

	renderMessage(c, nil)
}

// UserStatusResponse 用户状态响应
type UserStatusResponse struct {
	ID       string `json:"id"`
	Username string `json:"username"`
	Name     string `json:"name"`
	Email    string `json:"email"`
	IsActive bool   `json:"is_active"`
}

// @Summary 禁用用户
// @Description 禁用用户（在JumpServer中设置为非激活状态）
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param username path string true "用户名"
// @Success 200 {object} ApiResponse{dat=UserStatusResponse} "禁用成功"
// @Failure 400 {object} ApiResponse "请求参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 404 {object} ApiResponse "用户不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /users/{username}/disable [put]
func userDisable(c *gin.Context) {
	username := c.Param("username")
	if username == "" {
		bomb("username is required")
	}

	jsService, err := service.GetJumpServerServiceWithFallback()
	dangerous(err)

	// 禁用用户
	user, err := jsService.DisableUser(username)
	dangerous(err)

	response := UserStatusResponse{
		ID:       user.ID,
		Username: user.Username,
		Name:     user.Name,
		Email:    user.Email,
		IsActive: user.IsActive,
	}

	renderData(c, response, nil)
}

// @Summary 激活用户
// @Description 激活用户（在JumpServer中设置为激活状态）
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param username path string true "用户名"
// @Success 200 {object} ApiResponse{dat=UserStatusResponse} "激活成功"
// @Failure 400 {object} ApiResponse "请求参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 404 {object} ApiResponse "用户不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /users/{username}/enable [put]
func userEnable(c *gin.Context) {
	username := c.Param("username")
	if username == "" {
		bomb("username is required")
	}

	jsService, err := service.GetJumpServerServiceWithFallback()
	dangerous(err)

	// 激活用户
	user, err := jsService.EnableUser(username)
	dangerous(err)

	response := UserStatusResponse{
		ID:       user.ID,
		Username: user.Username,
		Name:     user.Name,
		Email:    user.Email,
		IsActive: user.IsActive,
	}

	renderData(c, response, nil)
}

// UserHostsResponse 用户主机权限响应
type UserHostsResponse struct {
	Username string         `json:"username"`
	Hosts    []*models.Host `json:"hosts"`
	Total    int            `json:"total"`
}

// @Summary 查询用户权限的主机列表
// @Description 查询用户在JumpServer中有权限访问的主机列表
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param username path string true "用户名"
// @Success 200 {object} ApiResponse{dat=UserHostsResponse} "用户主机权限列表"
// @Failure 400 {object} ApiResponse "请求参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 404 {object} ApiResponse "用户不存在"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /users/{username}/hosts [get]
func userHosts(c *gin.Context) {
	username := c.Param("username")
	if username == "" {
		bomb("username is required")
	}

	jsService, err := service.GetJumpServerServiceWithFallback()
	dangerous(err)

	// 获取用户权限的主机
	hosts, err := jsService.GetUserHosts(username)
	dangerous(err)

	response := UserHostsResponse{
		Username: username,
		Hosts:    hosts,
		Total:    len(hosts),
	}

	renderData(c, response, nil)
}

// HostSyncRequest 主机同步请求
type HostSyncRequest struct {
	HostIDs []int64 `json:"host_ids" binding:"required"`
}

// @Summary 同步主机到JumpServer
// @Description 将AMS中的主机同步到JumpServer的jump-ams节点下
// @Tags 主机管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param sync body HostSyncRequest true "同步主机信息"
// @Success 200 {object} ApiResponse "同步成功"
// @Failure 400 {object} ApiResponse "请求参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /hosts/sync [post]
func hostSync(c *gin.Context) {
	var req HostSyncRequest
	bind(c, &req)

	jsService, err := service.GetJumpServerServiceWithFallback()
	dangerous(err)

	// 同步主机
	var errors []string
	for _, hostID := range req.HostIDs {
		if err := jsService.SyncHostToJumpServer(hostID); err != nil {
			errors = append(errors, err.Error())
		}
	}

	if len(errors) > 0 {
		c.JSON(http.StatusOK, gin.H{
			"err": "部分主机同步失败",
			"dat": gin.H{
				"errors": errors,
			},
		})
		return
	}

	renderMessage(c, nil)
}

// UserPermissionGrantRequest 用户权限授权请求
type UserPermissionGrantRequest struct {
	Username string   `json:"username" binding:"required"` // 用户名
	HostIPs  []string `json:"host_ips" binding:"required"` // 主机IP列表
}

// @Summary 批量给用户更新机器授权
// @Description 批量给用户更新机器授权(有则更新，无则新建)，使用用户名和IP地址作为参数。授权名称格式为"perf-用户名"。自动授权全部协议和全部账号
// @Tags 权限管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param permission body UserPermissionGrantRequest true "用户权限授权信息"
// @Success 200 {object} ApiResponse{dat=UserPermissionGrantResponse} "授权结果"
// @Failure 400 {object} ApiResponse "请求参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /permissions/grant-user [post]
func userPermissionGrant(c *gin.Context) {
	var req UserPermissionGrantRequest
	bind(c, &req)

	jsService, err := service.GetJumpServerServiceWithFallback()
	dangerous(err)

	// 执行用户权限授权（使用全部协议和全部账号）
	result, err := jsService.GrantUserPermissionsByUsernameAndIPs(req.Username, req.HostIPs)
	dangerous(err)

	renderData(c, result, nil)
}

// UserPermissionRevokeRequest 用户权限回收请求
type UserPermissionRevokeRequest struct {
	Username string   `json:"username" binding:"required"` // 用户名
	HostIPs  []string `json:"host_ips"`                    // 主机IP列表，可选。如果为空则删除整个权限规则
}

// @Summary 回收用户权限
// @Description 回收指定用户的访问权限。如果提供host_ips则只回收指定机器的权限，如果不提供host_ips则删除整个权限规则
// @Tags 权限管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param permission body UserPermissionRevokeRequest true "用户权限回收信息"
// @Success 200 {object} ApiResponse{dat=UserPermissionRevokeResponse} "回收结果"
// @Failure 400 {object} ApiResponse "请求参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /permissions/revoke-user [post]
func userPermissionRevoke(c *gin.Context) {
	var req UserPermissionRevokeRequest
	bind(c, &req)

	jsService, err := service.GetJumpServerServiceWithFallback()
	dangerous(err)

	result, err := jsService.RevokeUserPermissionsByUsernameAndIPs(req.Username, req.HostIPs)
	dangerous(err)

	renderData(c, result, nil)
}

// UserPermissionGrantResponse 用户权限授权响应
type UserPermissionGrantResponse struct {
	Username       string                `json:"username"`        // 用户名
	PermissionName string                `json:"permission_name"` // 授权名称
	SuccessHosts   []string              `json:"success_hosts"`   // 成功授权的主机IP
	FailedHosts    []HostPermissionError `json:"failed_hosts"`    // 失败的主机及错误信息
	TotalHosts     int                   `json:"total_hosts"`     // 总主机数
	SuccessCount   int                   `json:"success_count"`   // 成功数量
	FailedCount    int                   `json:"failed_count"`    // 失败数量
}

// UserPermissionRevokeResponse 用户权限回收响应
type UserPermissionRevokeResponse struct {
	Username     string                `json:"username"`      // 用户名
	SuccessHosts []string              `json:"success_hosts"` // 成功回收的主机IP
	FailedHosts  []HostPermissionError `json:"failed_hosts"`  // 失败的主机及错误信息
	TotalHosts   int                   `json:"total_hosts"`   // 总主机数
	SuccessCount int                   `json:"success_count"` // 成功数量
	FailedCount  int                   `json:"failed_count"`  // 失败数量
}

// HostPermissionError 主机权限操作错误信息
type HostPermissionError struct {
	HostIP string `json:"host_ip"` // 主机IP
	Error  string `json:"error"`   // 错误信息
}

// UnsyncedHostsResponse 未同步主机列表响应
type UnsyncedHostsResponse struct {
	List  []*models.Host `json:"list"`  // 未同步主机列表
	Total int            `json:"total"` // 总数量
}

// @Summary 获取未同步到JumpServer的主机列表
// @Description 查询AMS数据库中jump_server_asset_id字段为空或null的主机记录，支持分页和多字段查询过滤
// @Tags 主机管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param limit query int false "每页数量，默认20" default(20)
// @Param p query int false "页码，默认1" default(1)
// @Param ip query string false "IP地址过滤，支持批量查询，多个值用逗号分隔" example("***********,***********")
// @Param name query string false "主机名过滤，支持批量查询，多个值用逗号分隔" example("web-server,db-server")
// @Param cate query string false "类别过滤，支持批量查询，多个值用逗号分隔" example("host,vm,container")
// @Param tenant query string false "租户过滤，支持批量查询，多个值用逗号分隔" example("team-a,team-b")
// @Param idc query string false "IDC机房过滤，支持批量查询，多个值用逗号分隔" example("beijing,shanghai")
// @Param manufacturer query string false "厂商过滤，支持批量查询，多个值用逗号分隔" example("Dell,HP,Lenovo")
// @Success 200 {object} ApiResponse{dat=UnsyncedHostsResponse} "未同步主机列表"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /hosts/unsynced [get]
func getUnsyncedHosts(c *gin.Context) {
	// 解析分页参数
	limit := queryInt(c, "limit", 20)
	offset := (queryInt(c, "p", 1) - 1) * limit

	// 构建查询条件 - 主要条件是jump_server_asset_id为空
	where := "jump_server_asset_id = '' OR jump_server_asset_id IS NULL"
	args := make([]interface{}, 0)

	// 支持的过滤字段
	filterFields := []string{"ip", "name", "cate", "tenant", "idc", "manufacturer", "sn", "ident", "model", "zone", "rack"}

	// 构建过滤条件
	for _, field := range filterFields {
		if value := c.Query(field); value != "" {
			// 支持批量查询，多个值用逗号分隔
			values := strings.Split(value, ",")
			if len(values) == 1 {
				// 单个值查询
				where += fmt.Sprintf(" AND %s = ?", field)
				args = append(args, strings.TrimSpace(value))
			} else {
				// 批量查询
				placeholders := make([]string, len(values))
				for i, v := range values {
					placeholders[i] = "?"
					args = append(args, strings.TrimSpace(v))
				}
				where += fmt.Sprintf(" AND %s IN (%s)", field, strings.Join(placeholders, ","))
			}
		}
	}

	// 使用数据库会话进行查询
	session := models.DB["ams"].Table(new(models.Host))
	if where != "" {
		session = session.Where(where, args...)
	}

	// 查询总数
	total, err := session.Clone().Count()
	if err != nil {
		logger.Errorf("Failed to get unsynced hosts total count: %v", err)
		dangerous(err)
		return
	}

	// 查询主机列表
	var hosts []models.Host
	err = session.Limit(limit, offset).OrderBy("ident").Find(&hosts)
	if err != nil {
		logger.Errorf("Failed to get unsynced hosts list: %v", err)
		dangerous(err)
		return
	}

	// 转换为指针切片以保持与其他API的一致性
	hostPtrs := make([]*models.Host, len(hosts))
	for i := range hosts {
		hostPtrs[i] = &hosts[i]
	}

	response := UnsyncedHostsResponse{
		List:  hostPtrs,
		Total: int(total),
	}

	renderData(c, response, nil)
}
