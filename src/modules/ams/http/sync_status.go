package http

import (
	"arboris/src/modules/ams/service"
	"github.com/gin-gonic/gin"
)

// @Summary 获取同步管理器状态
// @Description 获取全局同步管理器的状态和统计信息，用于监控和调试
// @Tags 系统管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Success 200 {object} ApiResponse{dat=map[string]interface{}} "同步管理器状态信息"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /sync/status [get]
func getSyncStatus(c *gin.Context) {
	stats := service.GetGlobalSyncManagerStats()
	
	// 添加额外的运行时信息
	stats["can_get_manager"] = false
	if manager, err := service.GetGlobalSyncManager(); err == nil && manager != nil {
		stats["can_get_manager"] = true
	}
	
	renderData(c, stats, nil)
}

// @Summary 重置同步管理器
// @Description 重置全局同步管理器，重新建立JumpServer连接。用于配置更新后的重连
// @Tags 系统管理
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Success 200 {object} ApiResponse "重置成功"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /sync/reset [post]
func resetSyncManager(c *gin.Context) {
	if err := service.ResetGlobalSyncManager(); err != nil {
		bomb("Failed to reset sync manager: %v", err)
		return
	}
	
	renderMessage(c, "Sync manager reset successfully")
}
