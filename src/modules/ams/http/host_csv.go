package http

import (
	"arboris/src/modules/ams/service"
	"bytes"
	"context"
	"crypto/rand"
	"encoding/csv"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"github.com/toolkits/pkg/logger"
	"io"
	"mime/multipart"
	"net"
	"net/http"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"sync"
	"time"

	"github.com/xuri/excelize/v2"

	"arboris/src/models"
	"github.com/gin-gonic/gin"
)

var cleanupMutex sync.Mutex

// 并发控制信号量，限制同时处理的导入请求数量
var importSemaphore = make(chan struct{}, 3) // 最多同时处理3个导入请求

// isValidIP 验证IP地址格式是否正确
func isValidIP(ip string) bool {
	// 去除首尾空格
	ip = strings.TrimSpace(ip)

	// 检查是否为空
	if ip == "" {
		return false
	}

	// 使用net.ParseIP验证IP格式
	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		return false
	}

	// 额外检查：排除一些特殊的无效IP
	// 0.0.0.0 通常不是有效的主机IP
	if ip == "0.0.0.0" {
		return false
	}

	// *************** 是广播地址，通常不是有效的主机IP
	if ip == "***************" {
		return false
	}

	return true
}

func init() {
	go func() {
		ticker := time.NewTicker(1 * time.Hour) // 每小时清理一次
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				cleanupExpiredFailedData()
			}
		}
	}()
}

// 清理过期的失败数据
func cleanupExpiredFailedData() {
	cleanupMutex.Lock()
	defer cleanupMutex.Unlock()

	now := time.Now().Unix()
	_, err := models.DB["ams"].Exec("DELETE FROM host_import_failed_data WHERE expires_at < ?", now)
	if err != nil {
		logger.Errorf("Failed to cleanup expired failed data: %v\n", err)
	}
}

func storeFailedData(importId string, failedRows []FailedRowData) error {
	if len(failedRows) == 0 {
		return nil
	}

	session := models.DB["ams"].NewSession()
	defer session.Close()

	if err := session.Begin(); err != nil {
		return fmt.Errorf("failed to start transaction: %v", err)
	}

	now := time.Now().Unix()
	expiresAt := now + 24*60*60 // 24小时后过期

	records := make([]*HostImportFailedData, len(failedRows))
	for i, failedRow := range failedRows {
		rowDataBytes, err := json.Marshal(failedRow.Data)
		if err != nil {
			session.Rollback()
			return fmt.Errorf("failed to marshal row data: %v", err)
		}

		records[i] = &HostImportFailedData{
			ImportId:  importId,
			RowNum:    failedRow.RowNum,
			RowData:   string(rowDataBytes),
			ErrorMsg:  failedRow.Error,
			CreatedAt: now,
			ExpiresAt: expiresAt,
		}
	}

	if _, err := session.Insert(records); err != nil {
		session.Rollback()
		return fmt.Errorf("failed to batch insert failed data: %v", err)
	}

	return session.Commit()
}

// 从数据库获取失败数据
func getFailedData(importId string) ([]FailedRowData, bool) {
	cleanupExpiredFailedData()

	var records []HostImportFailedData
	err := models.DB["ams"].Where("import_id = ?", importId).Find(&records)
	if err != nil {
		logger.Errorf("Failed to get failed data: %v\n", err)
		return nil, false
	}

	if len(records) == 0 {
		return nil, false
	}

	failedRows := make([]FailedRowData, len(records))
	for i, record := range records {
		var rowData []string
		if err := json.Unmarshal([]byte(record.RowData), &rowData); err != nil {
			logger.Errorf("Failed to unmarshal row data: %v\n", err)
			continue
		}

		failedRows[i] = FailedRowData{
			RowNum: record.RowNum,
			Data:   rowData,
			Error:  record.ErrorMsg,
		}
	}

	return failedRows, true
}

// 生成导入ID
func generateImportId() string {
	bytes := make([]byte, 16)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

// ImportResult CSV导入结果
type ImportResult struct {
	SuccessCount int    `json:"success_count" description:"成功导入数量"`
	FailedCount  int    `json:"failed_count" description:"失败数量"`
	TotalRows    int    `json:"total_rows" description:"总行数"`
	ImportId     string `json:"import_id,omitempty" description:"导入批次ID，用于下载失败数据。仅在有失败数据时返回"`
	// 同步结果信息
	SyncEnabled     bool   `json:"sync_enabled" description:"是否启用了自动同步"`
	SyncSuccessCount int   `json:"sync_success_count,omitempty" description:"同步成功数量"`
	SyncFailedCount  int   `json:"sync_failed_count,omitempty" description:"同步失败数量"`
	SyncErrors      []string `json:"sync_errors,omitempty" description:"同步错误信息"`
}

// FailedRowData 失败行数据
type FailedRowData struct {
	RowNum int      `json:"row_num" description:"行号"`
	Data   []string `json:"data" description:"行数据"`
	Error  string   `json:"error" description:"错误信息"`
}

// hostData 主机数据结构，用于批量处理
type hostData struct {
	host        *models.Host
	fieldValues []models.HostFieldValue
	rowNum      int
}

// HostImportFailedData 数据库模型
type HostImportFailedData struct {
	Id        int64  `json:"id" xorm:"'id' pk autoincr"`
	ImportId  string `json:"import_id" xorm:"'import_id'"`
	RowNum    int    `json:"row_num" xorm:"'row_num'"`
	RowData   string `json:"row_data" xorm:"'row_data'"`
	ErrorMsg  string `json:"error_msg" xorm:"'error_msg'"`
	CreatedAt int64  `json:"created_at" xorm:"'created_at'"`
	ExpiresAt int64  `json:"expires_at" xorm:"'expires_at'"`
}

// @Summary 下载主机导入模板
// @Description 动态根据自定义字段生成主机导入模板文件，支持CSV和Excel格式
// @Tags 主机管理
// @Accept json
// @Produce application/octet-stream
// @Security ApiKeyAuth
// @Param format query string false "文件格式" Enums(csv,xlsx) default(csv)
// @Success 200 {file} file "模板文件"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /hosts/csv/template [get]
func GetHostCSVTemplate(c *gin.Context) {
	format := strings.ToLower(c.DefaultQuery("format", "csv"))

	headers, err := generateHeaders()
	dangerous(err)

	example := []string{
		"SN001",
		"*************",
		"*************",
		"Web-server-01",
		"CentOS Linux 7 (Core)",
		"3.10.0-1160.el7.x86_64",
		"Intel Xeon E5-2680 v4",
		"2.40GHz * 2",
		"64GB DDR4",
		"SSD 500GB + HDD 2TB",
		"host",
		"8",
		"NVIDIA Tesla V100 * 2",
		"Dell PowerEdge R740",
		"北京机房",
		"可用区1",
		"A12",
		"Dell",
		"备注信息",
	}
	// 为自定义字段添加示例值
	customFieldCount := len(headers) - 19 // 基础字段有19个
	for i := 0; i < customFieldCount; i++ {
		example = append(example, "示例值")
	}

	switch format {
	case "xlsx":
		generateExcelTemplate(c, headers, example)
	case "csv":
		generateCSVTemplate(c, headers, example)
	default:
		bomb("unsupported format '%s'. Only 'csv' and 'xlsx' are supported", format)
	}
}

// generateCSVTemplate 生成CSV模板
func generateCSVTemplate(c *gin.Context, headers, example []string) {
	buf := new(bytes.Buffer)
	// 添加 UTF-8 BOM 以确保中文字符在 Excel 中正确显示
	buf.Write([]byte{0xEF, 0xBB, 0xBF})
	writer := csv.NewWriter(buf)

	if err := writer.Write(headers); err != nil {
		bomb("failed to write CSV headers: %v", err)
	}

	if err := writer.Write(example); err != nil {
		bomb("failed to write example row: %v", err)
	}

	writer.Flush()

	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Disposition", "attachment; filename=host_template.csv; filename*=UTF-8''%E4%B8%BB%E6%9C%BA%E6%A8%A1%E6%9D%BF.csv")
	c.Header("Content-Type", "text/csv; charset=utf-8")
	c.Header("Cache-Control", "no-cache")
	c.Header("Content-Transfer-Encoding", "binary")
	c.Data(http.StatusOK, "application/octet-stream", buf.Bytes())
}

// generateExcelTemplate 生成Excel模板
func generateExcelTemplate(c *gin.Context, headers, example []string) {
	f := excelize.NewFile()
	defer f.Close()

	// 设置工作表名称
	sheetName := "主机导入模板"
	index, err := f.NewSheet(sheetName)
	if err != nil {
		bomb("failed to create Excel sheet: %v", err)
	}

	// 删除默认的Sheet1
	f.DeleteSheet("Sheet1")

	// 设置活动工作表
	f.SetActiveSheet(index)

	// 写入标题行
	for i, header := range headers {
		cell := fmt.Sprintf("%s1", string(rune('A'+i)))
		f.SetCellValue(sheetName, cell, header)
	}

	// 写入示例行
	for i, value := range example {
		cell := fmt.Sprintf("%s2", string(rune('A'+i)))
		f.SetCellValue(sheetName, cell, value)
	}

	// 设置标题行样式
	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold: true,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#E0E0E0"},
			Pattern: 1,
		},
	})
	if err == nil {
		f.SetRowStyle(sheetName, 1, 1, style)
	}

	// 自动调整列宽
	for i := range headers {
		col := string(rune('A' + i))
		f.SetColWidth(sheetName, col, col, 15)
	}

	// 生成文件内容
	buf, err := f.WriteToBuffer()
	if err != nil {
		bomb("failed to generate Excel file: %v", err)
	}

	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Disposition", "attachment; filename=host_template.xlsx; filename*=UTF-8''%E4%B8%BB%E6%9C%BA%E6%A8%A1%E6%9D%BF.xlsx")
	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Header("Cache-Control", "no-cache")
	c.Header("Content-Transfer-Encoding", "binary")
	c.Data(http.StatusOK, "application/octet-stream", buf.Bytes())
}

// parseCSVFile 解析CSV文件
func parseCSVFile(file *multipart.FileHeader) ([][]string, error) {
	f, err := file.Open()
	if err != nil {
		return nil, fmt.Errorf("failed to open CSV file: %v", err)
	}
	defer f.Close()

	// 使用超时控制解析CSV文件
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	var records [][]string
	done := make(chan error, 1)

	go func() {
		defer func() {
			if r := recover(); r != nil {
				done <- fmt.Errorf("CSV parsing panic: %v", r)
			}
		}()

		reader := csv.NewReader(f)
		var err error
		records, err = reader.ReadAll()
		done <- err
	}()

	// 设置10秒超时
	select {
	case err := <-done:
		if err != nil {
			return nil, fmt.Errorf("failed to parse CSV file: %v", err)
		}
	case <-ctx.Done():
		return nil, fmt.Errorf("CSV file parsing timeout. The file may be too large or contain complex data. Please check the file and try again")
	}

	return records, nil
}

// parseExcelFile 解析Excel文件（支持.xls和.xlsx）
func parseExcelFile(file *multipart.FileHeader) ([][]string, error) {
	f, err := file.Open()
	if err != nil {
		return nil, fmt.Errorf("failed to open Excel file: %v", err)
	}
	defer f.Close()

	// 获取原始文件扩展名
	originalExt := strings.ToLower(filepath.Ext(file.Filename))

	// 创建临时文件，保持原始扩展名
	var tempFile *os.File
	if originalExt == ".xls" {
		tempFile, err = os.CreateTemp("", "excel_import_*.xls")
	} else {
		tempFile, err = os.CreateTemp("", "excel_import_*.xlsx")
	}

	if err != nil {
		return nil, fmt.Errorf("failed to create temp file: %v", err)
	}

	// 确保临时文件被清理，即使发生panic
	tempFileName := tempFile.Name()
	defer func() {
		tempFile.Close()
		os.Remove(tempFileName)
	}()

	// 将上传的文件内容复制到临时文件，设置超时
	done := make(chan error, 1)
	go func() {
		_, err := io.Copy(tempFile, f)
		done <- err
	}()

	// 设置5秒超时
	select {
	case err := <-done:
		if err != nil {
			return nil, fmt.Errorf("failed to copy file content: %v", err)
		}
	case <-time.After(5 * time.Second):
		return nil, fmt.Errorf("file copy timeout. The file may contain large attachments. Please remove attachments and try again")
	}

	// 关闭临时文件以便excelize读取
	tempFile.Close()

	// 使用excelize打开Excel文件，设置超时
	var xlsx *excelize.File
	done2 := make(chan error, 1)
	go func() {
		var err error
		xlsx, err = excelize.OpenFile(tempFile.Name())
		done2 <- err
	}()

	// 设置15秒超时（Excel文件解析可能需要更长时间）
	select {
	case err := <-done2:
		if err != nil {
			// 如果是.xls文件且打开失败，提供更详细的错误信息
			if originalExt == ".xls" {
				return nil, fmt.Errorf("unsupported workbook file format or file contains attachments. The .xls format (Excel 97-2003) may not be fully supported. Please convert your file to .xlsx format, remove any attachments, and try again")
			}
			return nil, fmt.Errorf("failed to open Excel file (may contain unsupported attachments): %v", err)
		}
	case <-time.After(15 * time.Second):
		return nil, fmt.Errorf("Excel file parsing timeout. The file may contain large attachments or complex formatting. Please save as a simple CSV or XLSX file without attachments")
	}
	defer xlsx.Close()

	// 获取第一个工作表
	sheets := xlsx.GetSheetList()
	if len(sheets) == 0 {
		return nil, fmt.Errorf("no sheets found in Excel file")
	}

	// 检查文件是否包含图片或其他附件
	sheetName := sheets[0]
	if err := detectExcelAttachments(xlsx, sheetName); err != nil {
		return nil, err
	}

	// 读取第一个工作表的所有行
	rows, err := xlsx.GetRows(sheetName)
	if err != nil {
		return nil, fmt.Errorf("failed to read Excel rows: %v", err)
	}

	// 清理和验证数据行
	cleanedRows, err := cleanExcelRows(rows, file.Filename)
	if err != nil {
		return nil, err
	}

	return cleanedRows, nil
}

// detectExcelAttachments 检测Excel文件是否包含图片或其他附件
func detectExcelAttachments(xlsx *excelize.File, sheetName string) error {
	// 检查是否有图片 - 遍历所有可能的单元格
	// 由于GetPictures需要指定单元格，我们采用其他方法检测

	// 尝试获取工作表的图片信息（通过检查工作表的关系）
	// 这是一个简化的检测方法
	defer func() {
		if r := recover(); r != nil {
			// 如果检测图片时出现panic，忽略并继续
		}
	}()

	// 检查工作表是否包含图片的简单方法：检查文件大小和内容复杂度
	// 如果Excel文件包含图片，通常会有更多的内部结构

	// 检查工作表是否有异常的维度（可能表示有隐藏内容）
	rows, err := xlsx.GetRows(sheetName)
	if err != nil {
		return nil // 如果无法读取行，让后续处理来处理错误
	}

	// 检查是否有过多的空行（可能表示有隐藏对象）
	emptyRowCount := 0
	totalRows := len(rows)

	for _, row := range rows {
		isEmpty := true
		for _, cell := range row {
			if strings.TrimSpace(cell) != "" {
				isEmpty = false
				break
			}
		}
		if isEmpty {
			emptyRowCount++
		}
	}

	// 如果空行比例超过50%且总行数超过10行，可能包含隐藏内容
	if totalRows > 10 && float64(emptyRowCount)/float64(totalRows) > 0.5 {
		return fmt.Errorf("Excel file appears to contain hidden content or formatting that may interfere with data parsing. Please copy the data to a new Excel file or save as CSV format and try again")
	}

	return nil
}

// cleanExcelRows 清理和验证Excel行数据
func cleanExcelRows(rows [][]string, filename string) ([][]string, error) {
	if len(rows) == 0 {
		return nil, fmt.Errorf("Excel file is empty")
	}

	// 找到第一个非空行作为表头
	var headerRowIndex = -1
	var headerRow []string

	for i, row := range rows {
		hasContent := false
		for _, cell := range row {
			if strings.TrimSpace(cell) != "" {
				hasContent = true
				break
			}
		}
		if hasContent {
			headerRowIndex = i
			headerRow = row
			break
		}
	}

	if headerRowIndex == -1 {
		return nil, fmt.Errorf("Excel file contains no data rows")
	}

	// 检查表头是否包含必要的字段
	hasIP := false
	hasIdent := false
	hasName := false
	hasCate := false

	for _, header := range headerRow {
		header = strings.TrimSpace(header)
		switch header {
		case "IP地址", "ip":
			hasIP = true
		case "标识符", "ident":
			hasIdent = true
		case "主机名", "name":
			hasName = true
		case "类别", "cate":
			hasCate = true
		}
	}

	missingHeaders := []string{}
	if !hasIP {
		missingHeaders = append(missingHeaders, "IP地址")
	}
	if !hasIdent {
		missingHeaders = append(missingHeaders, "标识符")
	}
	if !hasName {
		missingHeaders = append(missingHeaders, "主机名")
	}
	if !hasCate {
		missingHeaders = append(missingHeaders, "类别")
	}

	if len(missingHeaders) > 0 {
		return nil, fmt.Errorf("Excel file is missing required column headers: %s. Please ensure your Excel file has the correct headers. If the file contains images or complex formatting, please remove them and save as a simple CSV or XLSX file", strings.Join(missingHeaders, ", "))
	}

	// 构建清理后的数据
	cleanedRows := [][]string{}

	// 添加表头
	cleanedRows = append(cleanedRows, headerRow)

	// 处理数据行
	dataRowCount := 0
	for i := headerRowIndex + 1; i < len(rows); i++ {
		row := rows[i]

		// 检查行是否有有效数据
		hasValidData := false
		for j, cell := range row {
			if j < len(headerRow) && strings.TrimSpace(cell) != "" {
				hasValidData = true
				break
			}
		}

		if hasValidData {
			// 确保行的长度与表头一致
			normalizedRow := make([]string, len(headerRow))
			for j := 0; j < len(headerRow); j++ {
				if j < len(row) {
					normalizedRow[j] = strings.TrimSpace(row[j])
				} else {
					normalizedRow[j] = ""
				}
			}
			cleanedRows = append(cleanedRows, normalizedRow)
			dataRowCount++
		}
	}

	if dataRowCount == 0 {
		return nil, fmt.Errorf("Excel file contains headers but no valid data rows. If the file contains images or complex formatting, please remove them and save as a simple CSV or XLSX file")
	}

	logger.Infof("[ExcelParse] Successfully parsed Excel file '%s': %d header columns, %d data rows", filename, len(headerRow), dataRowCount)

	return cleanedRows, nil
}

// generateHeaders 生成统一的CSV头部，确保模板下载和失败数据下载保持一致
func generateHeaders() ([]string, error) {
	fields, err := models.HostFieldGets()
	if err != nil {
		return nil, err
	}

	headers := []string{
		"序列号", "IP地址", "标识符", "主机名",
		"操作系统版本", "内核版本", "CPU型号", "CPU核数", "内存", "磁盘",
		"类别",
		"GPU卡数", "GPU型号", "型号", "地域", "可用区", "机架", "厂商", "备注",
	}
	for _, field := range fields {
		headers = append(headers, field.FieldIdent)
	}
	return headers, nil
}

// parseFileByExtension 根据文件扩展名选择解析方法
func parseFileByExtension(file *multipart.FileHeader) ([][]string, error) {
	filename := file.Filename
	ext := strings.ToLower(filepath.Ext(filename))

	switch ext {
	case ".csv":
		return parseCSVFile(file)
	case ".xlsx":
		return parseExcelFile(file)
	default:
		return nil, fmt.Errorf("unsupported file format '%s'. Only CSV (.csv) and Excel (.xlsx) files are supported. XLS format is no longer supported, please convert to CSV or XLSX", ext)
	}
}

// StreamRowProcessor 流式行处理器接口
type StreamRowProcessor interface {
	ProcessHeader(headers []string) error
	ProcessRow(rowNum int, record []string) error
	GetResults() (successCount int, failedCount int, totalRows int, importId string, hasErrors bool)
	Cleanup()
}

// HostStreamProcessor 主机导入流式处理器
type HostStreamProcessor struct {
	headerMap map[string]int
	fieldMap  map[string]models.HostField
	username  string

	// 统计信息
	successCount int
	failedCount  int
	totalRows    int

	// 错误处理
	errors     []string
	failedRows []FailedRowData
	importId   string

	// 批量处理缓冲区
	batchBuffer []hostData
	batchSize   int

	// 重复检查缓存（滑动窗口）
	recentIPs    map[string]int // IP -> 行号
	recentIdents map[string]int // Ident -> 行号
	cacheSize    int

	// 性能优化：全局重复检查缓存
	globalDuplicateCache map[string]bool
	duplicateCheckBatch  []string
	duplicateCheckSize   int

	// 性能优化：字段值批量插入缓冲区
	fieldValueBuffer    []models.HostFieldValue
	fieldValueBatchSize int

	// 同步相关
	enableSync       bool
	successfulHosts  []*models.Host // 成功导入的主机，用于同步
	syncSuccessCount int
	syncFailedCount  int
	syncErrors       []string
}

// NewHostStreamProcessor 创建新的流式处理器
func NewHostStreamProcessor(username string, enableSync bool) (*HostStreamProcessor, error) {
	hostFields, err := models.HostFieldGets()
	if err != nil {
		return nil, fmt.Errorf("Failed to get host fields: %v", err)
	}

	fieldMap := make(map[string]models.HostField)
	for _, field := range hostFields {
		fieldMap[field.FieldIdent] = field
	}

	processor := &HostStreamProcessor{
		fieldMap:             fieldMap,
		username:             username,
		batchSize:            200, // 增大批量大小，提高性能
		recentIPs:            make(map[string]int),
		recentIdents:         make(map[string]int),
		cacheSize:            500, // 增大缓存，减少数据库查询
		globalDuplicateCache: make(map[string]bool),
		duplicateCheckBatch:  make([]string, 0, 200),
		duplicateCheckSize:   200,
		fieldValueBuffer:     make([]models.HostFieldValue, 0, 1000),
		fieldValueBatchSize:  1000, // 字段值批量插入
		enableSync:           enableSync,
		successfulHosts:      make([]*models.Host, 0),
		syncErrors:           make([]string, 0),
	}

	return processor, nil
}

// ProcessHeader 处理表头
func (p *HostStreamProcessor) ProcessHeader(headers []string) error {
	p.headerMap = make(map[string]int)
	for i, header := range headers {
		p.headerMap[strings.TrimSpace(header)] = i
	}
	return nil
}

// ProcessRow 处理单行数据
func (p *HostStreamProcessor) ProcessRow(rowNum int, record []string) error {
	p.totalRows++

	// 安全获取字段值的辅助函数
	getSafeValue := func(chineseName, englishName string) string {
		if idx, exists := p.headerMap[chineseName]; exists && idx < len(record) {
			return strings.TrimSpace(record[idx])
		}
		if idx, exists := p.headerMap[englishName]; exists && idx < len(record) {
			return strings.TrimSpace(record[idx])
		}
		return ""
	}

	// 获取必填字段
	ip := getSafeValue("IP地址", "ip")
	ident := getSafeValue("标识符", "ident")
	name := getSafeValue("主机名", "name")
	cate := getSafeValue("类别", "cate")

	// 基础字段验证
	if err := p.validateRequiredFields(rowNum, record, ip, ident, name, cate); err != nil {
		return nil // 错误已记录，继续处理下一行
	}

	// 字段长度验证
	if err := p.validateFieldLengths(rowNum, record, getSafeValue); err != nil {
		return nil // 错误已记录，继续处理下一行
	}

	// IP格式验证
	if !isValidIP(ip) {
		p.addError(rowNum, record, fmt.Sprintf("Row %d: Invalid IP address format '%s'", rowNum, ip))
		return nil
	}

	// 检查最近行的重复
	if err := p.checkRecentDuplicates(rowNum, record, ip, ident); err != nil {
		return nil // 错误已记录
	}

	// 创建主机对象
	host := &models.Host{
		SN:            getSafeValue("序列号", "sn"),
		IP:            ip,
		Ident:         ident,
		Tenant:        "",
		Name:          name,
		OSVersion:     getSafeValue("操作系统版本", "os_version"),
		KernelVersion: getSafeValue("内核版本", "kernel_version"),
		CPUModel:      getSafeValue("CPU型号", "cpu_model"),
		CPU:           getSafeValue("CPU核数", "cpu"),
		Mem:           getSafeValue("内存", "mem"),
		Disk:          getSafeValue("磁盘", "disk"),
		Note:          getSafeValue("备注", "note"),
		Cate:          cate,
		GPU:           getSafeValue("GPU卡数", "gpu"),
		GPUModel:      getSafeValue("GPU型号", "gpu_model"),
		Model:         getSafeValue("型号", "model"),
		IDC:           getSafeValue("地域", "idc"),
		Manufacturer:  getSafeValue("厂商", "manufacturer"),
		Zone:          getSafeValue("可用区", "zone"),
		Rack:          getSafeValue("机架", "rack"),
		Clock:         time.Now().Unix(),
	}

	// 处理自定义字段
	fieldValues, hasError := p.processCustomFields(rowNum, record, getSafeValue)
	if hasError {
		return nil // 错误已记录
	}

	// 添加到批量处理缓冲区
	p.batchBuffer = append(p.batchBuffer, hostData{
		host:        host,
		fieldValues: fieldValues,
		rowNum:      rowNum,
	})

	// 更新重复检查缓存
	p.updateRecentCache(ip, ident, rowNum)

	// 如果缓冲区满了，执行批量处理
	if len(p.batchBuffer) >= p.batchSize {
		return p.processBatch()
	}

	return nil
}

// validateRequiredFields 验证必填字段
func (p *HostStreamProcessor) validateRequiredFields(rowNum int, record []string, ip, ident, name, cate string) error {
	var missingFields []string
	if ip == "" {
		missingFields = append(missingFields, "IP地址")
	}
	if ident == "" {
		missingFields = append(missingFields, "标识符")
	}
	if name == "" {
		missingFields = append(missingFields, "主机名")
	}
	if cate == "" {
		missingFields = append(missingFields, "类别")
	}

	if len(missingFields) > 0 {
		errorMsg := fmt.Sprintf("Row %d: %s are required", rowNum, strings.Join(missingFields, ", "))
		p.addError(rowNum, record, errorMsg)
		return fmt.Errorf("missing required fields")
	}
	return nil
}

// validateFieldLengths validates field length limits
func (p *HostStreamProcessor) validateFieldLengths(rowNum int, record []string, getSafeValue func(string, string) string) error {
	var lengthErrors []string

	// Check basic field lengths
	ip := getSafeValue("IP地址", "ip")
	if len(ip) > 15 {
		lengthErrors = append(lengthErrors, fmt.Sprintf("IP address exceeds 15 characters (current %d characters)", len(ip)))
	}

	ident := getSafeValue("标识符", "ident")
	if len(ident) > 128 {
		lengthErrors = append(lengthErrors, fmt.Sprintf("Identifier exceeds 128 characters (current %d characters)", len(ident)))
	}

	name := getSafeValue("主机名", "name")
	if len(name) > 128 {
		lengthErrors = append(lengthErrors, fmt.Sprintf("Host name exceeds 128 characters (current %d characters)", len(name)))
	}

	cate := getSafeValue("类别", "cate")
	if len(cate) > 32 {
		lengthErrors = append(lengthErrors, fmt.Sprintf("Category exceeds 32 characters (current %d characters)", len(cate)))
	}

	// Check other field lengths
	sn := getSafeValue("序列号", "sn")
	if len(sn) > 128 {
		lengthErrors = append(lengthErrors, fmt.Sprintf("Serial number exceeds 128 characters (current %d characters)", len(sn)))
	}

	osVersion := getSafeValue("操作系统版本", "os_version")
	if len(osVersion) > 255 {
		lengthErrors = append(lengthErrors, fmt.Sprintf("OS version exceeds 255 characters (current %d characters)", len(osVersion)))
	}

	kernelVersion := getSafeValue("内核版本", "kernel_version")
	if len(kernelVersion) > 255 {
		lengthErrors = append(lengthErrors, fmt.Sprintf("Kernel version exceeds 255 characters (current %d characters)", len(kernelVersion)))
	}

	cpuModel := getSafeValue("CPU型号", "cpu_model")
	if len(cpuModel) > 255 {
		lengthErrors = append(lengthErrors, fmt.Sprintf("CPU model exceeds 255 characters (current %d characters)", len(cpuModel)))
	}

	note := getSafeValue("备注", "note")
	if len(note) > 255 {
		lengthErrors = append(lengthErrors, fmt.Sprintf("Note exceeds 255 characters (current %d characters)", len(note)))
	}

	if len(lengthErrors) > 0 {
		errorMsg := fmt.Sprintf("Row %d: %s", rowNum, strings.Join(lengthErrors, "; "))
		p.addError(rowNum, record, errorMsg)
		return fmt.Errorf("field length validation failed")
	}

	return nil
}

// checkRecentDuplicates 检查最近行的重复
func (p *HostStreamProcessor) checkRecentDuplicates(rowNum int, record []string, ip, ident string) error {
	// 先检查全局缓存（已知的数据库重复）
	if p.globalDuplicateCache[ip] {
		errorMsg := fmt.Sprintf("Row %d: IP %s already exists in database", rowNum, ip)
		p.addError(rowNum, record, errorMsg)
		return fmt.Errorf("duplicate IP in database")
	}

	if p.globalDuplicateCache[ident] {
		errorMsg := fmt.Sprintf("Row %d: Ident %s already exists in database", rowNum, ident)
		p.addError(rowNum, record, errorMsg)
		return fmt.Errorf("duplicate ident in database")
	}

	// 再检查最近行的重复
	if existingRow, exists := p.recentIPs[ip]; exists {
		errorMsg := fmt.Sprintf("Row %d: IP %s already exists in row %d", rowNum, ip, existingRow)
		p.addError(rowNum, record, errorMsg)
		return fmt.Errorf("duplicate IP")
	}

	if existingRow, exists := p.recentIdents[ident]; exists {
		errorMsg := fmt.Sprintf("Row %d: Ident %s already exists in row %d", rowNum, ident, existingRow)
		p.addError(rowNum, record, errorMsg)
		return fmt.Errorf("duplicate ident")
	}

	return nil
}

// processCustomFields 处理自定义字段
func (p *HostStreamProcessor) processCustomFields(rowNum int, record []string, getSafeValue func(string, string) string) ([]models.HostFieldValue, bool) {
	fieldValues := make([]models.HostFieldValue, 0)

	for fieldIdent, field := range p.fieldMap {
		if idx, exists := p.headerMap[fieldIdent]; exists {
			value := strings.TrimSpace(record[idx])

			// 验证必填字段
			if field.FieldRequired == 1 && value == "" {
				errorMsg := fmt.Sprintf("Row %d: Required field %s is empty", rowNum, fieldIdent)
				p.addError(rowNum, record, errorMsg)
				return nil, true
			}

			// Validate field value length (custom field values max 1024 characters)
			if len(value) > 1024 {
				errorMsg := fmt.Sprintf("Row %d: Field %s value length exceeds 1024 characters (current %d characters)", rowNum, fieldIdent, len(value))
				p.addError(rowNum, record, errorMsg)
				return nil, true
			}

			if value != "" {
				fieldValues = append(fieldValues, models.HostFieldValue{
					FieldIdent: fieldIdent,
					FieldValue: value,
				})
			}
		}
	}

	return fieldValues, false
}

// updateRecentCache 更新重复检查缓存
func (p *HostStreamProcessor) updateRecentCache(ip, ident string, rowNum int) {
	// 如果缓存超过限制，清理最旧的条目
	if len(p.recentIPs) >= p.cacheSize {
		// 简单的清理策略：清理一半
		newIPs := make(map[string]int)
		newIdents := make(map[string]int)

		// 保留最近的一半
		minRow := rowNum - p.cacheSize/2
		for k, v := range p.recentIPs {
			if v >= minRow {
				newIPs[k] = v
			}
		}
		for k, v := range p.recentIdents {
			if v >= minRow {
				newIdents[k] = v
			}
		}

		p.recentIPs = newIPs
		p.recentIdents = newIdents
	}

	p.recentIPs[ip] = rowNum
	p.recentIdents[ident] = rowNum
}

// addError 添加错误记录
func (p *HostStreamProcessor) addError(rowNum int, record []string, errorMsg string) {
	p.errors = append(p.errors, errorMsg)

	// 复制记录数据，因为原始数据可能被重用
	recordCopy := make([]string, len(record))
	copy(recordCopy, record)

	p.failedRows = append(p.failedRows, FailedRowData{
		RowNum: rowNum,
		Data:   recordCopy,
		Error:  errorMsg,
	})
	p.failedCount++
}

// processBatch 处理批量数据
func (p *HostStreamProcessor) processBatch() error {
	if len(p.batchBuffer) == 0 {
		return nil
	}

	// 收集需要检查的IP和Ident（排除已知重复的）
	ipsToCheck := make([]string, 0, len(p.batchBuffer))
	identsToCheck := make([]string, 0, len(p.batchBuffer))

	for _, hostData := range p.batchBuffer {
		if !p.globalDuplicateCache[hostData.host.IP] {
			ipsToCheck = append(ipsToCheck, hostData.host.IP)
		}
		if !p.globalDuplicateCache[hostData.host.Ident] {
			identsToCheck = append(identsToCheck, hostData.host.Ident)
		}
	}

	// 只有在有新数据时才查询数据库
	var duplicateMap map[string]bool
	if len(ipsToCheck) > 0 || len(identsToCheck) > 0 {
		var err error
		duplicateMap, err = models.HostCheckDuplicates(ipsToCheck, identsToCheck)
		if err != nil {
			return fmt.Errorf("Failed to check duplicates: %v", err)
		}

		// 更新全局缓存
		for key, isDuplicate := range duplicateMap {
			if isDuplicate {
				p.globalDuplicateCache[key] = true
			}
		}
	} else {
		duplicateMap = make(map[string]bool)
	}

	// 过滤掉重复的主机
	validHosts := make([]hostData, 0, len(p.batchBuffer))
	for _, hostData := range p.batchBuffer {
		if p.globalDuplicateCache[hostData.host.IP] || p.globalDuplicateCache[hostData.host.Ident] ||
			duplicateMap[hostData.host.IP] || duplicateMap[hostData.host.Ident] {
			errorMsg := fmt.Sprintf("Row %d: Duplicate IP or Ident found in database", hostData.rowNum)
			// 需要重新构造record数据用于错误记录
			record := []string{hostData.host.IP, hostData.host.Ident, hostData.host.Name, hostData.host.Cate}
			p.addError(hostData.rowNum, record, errorMsg)
			continue
		}
		validHosts = append(validHosts, hostData)
	}

	// 批量插入有效的主机
	if len(validHosts) > 0 {
		hostsToInsert := make([]*models.Host, len(validHosts))
		for i, hostData := range validHosts {
			hostsToInsert[i] = hostData.host
		}

		// 使用支持错误处理的批量插入方法
		successHosts, failedHosts, err := models.HostBatchNewWithErrorHandling(hostsToInsert)
		if err != nil {
			return fmt.Errorf("Failed to batch insert hosts: %v", err)
		}

		// Handle failed host insertions
		if len(failedHosts) > 0 {
			// Create a mapping to quickly find original data for failed hosts
			hostToDataMap := make(map[*models.Host]hostData)
			for i, hostData := range validHosts {
				hostToDataMap[hostsToInsert[i]] = hostData
			}

			for _, failedHost := range failedHosts {
				if originalData, exists := hostToDataMap[failedHost.Host]; exists {
					// 从原始记录中重建record数据用于错误报告
					errorMsg := fmt.Sprintf("Row %d: Database insertion failed - %s", originalData.rowNum, failedHost.Error)
					p.addError(originalData.rowNum, []string{}, errorMsg) // 空record，因为我们已经在批量处理阶段
				}
			}
		}

		// 收集成功插入主机的字段值到缓冲区
		successHostMap := make(map[*models.Host]bool)
		for _, host := range successHosts {
			successHostMap[host] = true
		}

		for i, hostData := range validHosts {
			if successHostMap[hostsToInsert[i]] && len(hostData.fieldValues) > 0 {
				// 设置host_id
				for j := range hostData.fieldValues {
					hostData.fieldValues[j].HostId = hostsToInsert[i].Id
				}
				// 添加到字段值缓冲区
				p.fieldValueBuffer = append(p.fieldValueBuffer, hostData.fieldValues...)
			}
		}

		// 如果字段值缓冲区满了，批量插入
		if len(p.fieldValueBuffer) >= p.fieldValueBatchSize {
			if err := p.flushFieldValues(); err != nil {
				logger.Errorf("Failed to flush field values: %v", err)
			}
		}

		p.successCount += len(successHosts)

		// 如果启用同步，收集成功的主机用于后续同步
		if p.enableSync {
			p.successfulHosts = append(p.successfulHosts, successHosts...)
		}

		// 将成功插入的IP和Ident加入全局缓存，避免后续重复检查
		for _, host := range successHosts {
			p.globalDuplicateCache[host.IP] = true
			p.globalDuplicateCache[host.Ident] = true
		}
	}

	// 清空缓冲区
	p.batchBuffer = p.batchBuffer[:0]

	return nil
}

// flushFieldValues 批量插入字段值
func (p *HostStreamProcessor) flushFieldValues() error {
	if len(p.fieldValueBuffer) == 0 {
		return nil
	}

	session := models.DB["ams"].NewSession()
	defer session.Close()

	if err := session.Begin(); err != nil {
		return fmt.Errorf("failed to start transaction: %v", err)
	}

	// 批量插入字段值
	_, err := session.Insert(p.fieldValueBuffer)
	if err != nil {
		session.Rollback()
		return fmt.Errorf("failed to batch insert field values: %v", err)
	}

	if err := session.Commit(); err != nil {
		return fmt.Errorf("failed to commit field values: %v", err)
	}

	// 清空缓冲区
	p.fieldValueBuffer = p.fieldValueBuffer[:0]

	return nil
}

// performSync 执行同步操作
func (p *HostStreamProcessor) performSync() {
	if !p.enableSync || len(p.successfulHosts) == 0 {
		return
	}

	logger.Infof("Starting auto-sync for %d successfully imported hosts", len(p.successfulHosts))
	start := time.Now()

	syncManager, err := service.NewSyncManager()
	if err != nil {
		p.syncErrors = append(p.syncErrors, fmt.Sprintf("Failed to create sync manager: %v", err))
		logger.Errorf("Failed to create sync manager for auto-sync: %v", err)
		return
	}

	// 收集主机ID
	hostIDs := make([]int64, len(p.successfulHosts))
	for i, host := range p.successfulHosts {
		hostIDs[i] = host.Id
	}

	// 执行批量同步
	results, err := syncManager.BatchSyncWithConsistency(hostIDs)
	if err != nil {
		p.syncErrors = append(p.syncErrors, fmt.Sprintf("Batch sync failed: %v", err))
		logger.Errorf("Batch sync failed during auto-sync: %v", err)
		return
	}

	// 统计同步结果
	for _, result := range results {
		if result.Success {
			p.syncSuccessCount++
		} else {
			p.syncFailedCount++
			if result.Error != "" {
				p.syncErrors = append(p.syncErrors, fmt.Sprintf("Host %d sync failed: %s", result.HostID, result.Error))
			}
		}
	}

	duration := time.Since(start)
	logger.Infof("Auto-sync completed in %v: %d success, %d failed, %d total",
		duration, p.syncSuccessCount, p.syncFailedCount, len(p.successfulHosts))
}

// GetResults 获取处理结果
func (p *HostStreamProcessor) GetResults() (successCount int, failedCount int, totalRows int, importId string, hasErrors bool, syncResult *SyncResult) {
	// 处理剩余的批量数据
	if len(p.batchBuffer) > 0 {
		if err := p.processBatch(); err != nil {
			logger.Errorf("Failed to process final batch: %v", err)
		}
	}

	// 插入剩余的字段值
	if len(p.fieldValueBuffer) > 0 {
		if err := p.flushFieldValues(); err != nil {
			logger.Errorf("Failed to flush final field values: %v", err)
		}
	}

	// 执行同步操作（如果启用）
	p.performSync()

	hasErrors = len(p.errors) > 0
	if hasErrors {
		// 生成导入ID并存储失败数据
		p.importId = generateImportId()
		if err := storeFailedData(p.importId, p.failedRows); err != nil {
			logger.Errorf("Failed to store failed data: %v", err)
			p.importId = "" // 清空importId，表示无法提供下载
		}
	}

	// 构建同步结果
	syncResult = &SyncResult{
		Enabled:      p.enableSync,
		SuccessCount: p.syncSuccessCount,
		FailedCount:  p.syncFailedCount,
		Errors:       p.syncErrors,
	}

	return p.successCount, p.failedCount, p.totalRows, p.importId, hasErrors, syncResult
}

// SyncResult 同步结果结构
type SyncResult struct {
	Enabled      bool     `json:"enabled"`
	SuccessCount int      `json:"success_count"`
	FailedCount  int      `json:"failed_count"`
	Errors       []string `json:"errors"`
}

// Cleanup 清理资源
func (p *HostStreamProcessor) Cleanup() {
	p.batchBuffer = nil
	p.recentIPs = nil
	p.recentIdents = nil
	p.errors = nil
	p.failedRows = nil
	p.duplicateCheckBatch = nil
	p.globalDuplicateCache = nil
	p.fieldValueBuffer = nil
	p.successfulHosts = nil
	p.syncErrors = nil

	// 强制垃圾回收
	runtime.GC()
}

// StreamFileParser 流式文件解析器接口
type StreamFileParser interface {
	ParseStream(file *multipart.FileHeader, processor StreamRowProcessor) error
}

// CSVStreamParser CSV流式解析器
type CSVStreamParser struct{}

func (p *CSVStreamParser) ParseStream(file *multipart.FileHeader, processor StreamRowProcessor) error {
	f, err := file.Open()
	if err != nil {
		return fmt.Errorf("failed to open CSV file: %v", err)
	}
	defer f.Close()

	reader := csv.NewReader(f)
	reader.ReuseRecord = true // 重用记录切片，减少内存分配

	rowNum := 0
	var headers []string

	for {
		record, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			return fmt.Errorf("failed to read CSV record at row %d: %v", rowNum+1, err)
		}

		rowNum++

		// 处理表头
		if rowNum == 1 {
			headers = make([]string, len(record))
			copy(headers, record)
			if err := processor.ProcessHeader(headers); err != nil {
				return fmt.Errorf("failed to process header: %v", err)
			}
			continue
		}

		// 检查行数限制
		if rowNum > 1001 { // 1000行数据 + 1行表头
			return fmt.Errorf("Too many rows to import. Maximum allowed: 1000 rows, but got more than 1000 rows")
		}

		// 优化：只在需要时复制记录数据
		// 对于大部分正常行，直接传递引用，减少内存分配
		// 只有在出错需要保存时才复制数据
		if err := processor.ProcessRow(rowNum, record); err != nil {
			return fmt.Errorf("failed to process row %d: %v", rowNum, err)
		}
	}

	if rowNum < 2 {
		return fmt.Errorf("CSV file must contain at least one data row")
	}

	return nil
}

// ExcelStreamParser Excel流式解析器（支持XLSX格式）
type ExcelStreamParser struct{}

func (p *ExcelStreamParser) ParseStream(file *multipart.FileHeader, processor StreamRowProcessor) error {
	// 对于Excel文件，我们仍然需要先解析到内存，但可以逐行处理
	// 这里可以考虑使用excelize的流式读取功能

	f, err := file.Open()
	if err != nil {
		return fmt.Errorf("failed to open Excel file: %v", err)
	}
	defer f.Close()

	// 创建临时文件
	tempFile, err := os.CreateTemp("", "excel_stream_*.xlsx")
	if err != nil {
		return fmt.Errorf("failed to create temp file: %v", err)
	}
	defer os.Remove(tempFile.Name())
	defer tempFile.Close()

	// 复制文件内容
	_, err = io.Copy(tempFile, f)
	if err != nil {
		return fmt.Errorf("failed to copy file content: %v", err)
	}
	tempFile.Close()

	// 使用excelize打开文件
	xlsx, err := excelize.OpenFile(tempFile.Name())
	if err != nil {
		return fmt.Errorf("failed to open Excel file: %v", err)
	}
	defer xlsx.Close()

	// 获取第一个工作表
	sheets := xlsx.GetSheetList()
	if len(sheets) == 0 {
		return fmt.Errorf("no sheets found in Excel file")
	}

	sheetName := sheets[0]

	// 检查文件是否包含图片或其他附件
	if err := detectExcelAttachments(xlsx, sheetName); err != nil {
		return err
	}

	rows, err := xlsx.GetRows(sheetName)
	if err != nil {
		return fmt.Errorf("failed to get rows from sheet: %v", err)
	}

	// 清理和验证数据行
	cleanedRows, err := cleanExcelRows(rows, file.Filename)
	if err != nil {
		return err
	}

	if len(cleanedRows) < 2 {
		return fmt.Errorf("Excel file must contain at least one data row")
	}

	if len(cleanedRows) > 1001 { // 1000行数据 + 1行表头
		return fmt.Errorf("Too many rows to import. Maximum allowed: 1000 rows, but got: %d rows", len(cleanedRows)-1)
	}

	// 处理表头
	if err := processor.ProcessHeader(cleanedRows[0]); err != nil {
		return fmt.Errorf("failed to process header: %v", err)
	}

	// 逐行处理数据
	for i := 1; i < len(cleanedRows); i++ {
		if err := processor.ProcessRow(i+1, cleanedRows[i]); err != nil {
			return fmt.Errorf("failed to process row %d: %v", i+1, err)
		}
	}

	return nil
}

// getStreamParser 根据文件扩展名获取流式解析器
func getStreamParser(filename string) (StreamFileParser, error) {
	ext := strings.ToLower(filepath.Ext(filename))

	switch ext {
	case ".csv":
		return &CSVStreamParser{}, nil
	case ".xlsx":
		return &ExcelStreamParser{}, nil
	default:
		return nil, fmt.Errorf("unsupported file format '%s'. Only CSV (.csv) and Excel (.xlsx) files are supported. XLS format is no longer supported, please convert to CSV or XLSX", ext)
	}
}

// @Summary 批量导入主机
// @Description 通过CSV或Excel文件批量导入主机数据，支持基础字段和自定义字段。支持.csv、.xlsx格式。最大支持1000行数据导入，文件大小限制10MB。必填字段：IP地址、标识符、主机名、类别。IP地址格式校验：必须是有效的IPv4或IPv6地址，不允许0.0.0.0和***************。注意：不支持包含大量附件（图片、嵌入文档等）的Excel文件，请移除附件后重试。返回导入结果统计信息，详细错误信息可通过失败数据下载接口获取
// @Tags 主机管理
// @Accept multipart/form-data
// @Produce json
// @Security ApiKeyAuth
// @Param file formData file true "CSV或Excel文件（支持.csv、.xlsx格式，最大1000行数据，文件大小限制10MB，不支持包含附件的文件）。必填字段：IP地址、标识符、主机名、类别。IP地址必须是有效格式，如*************"
// @Success 200 {object} ApiResponse{dat=ImportResult} "导入完成，返回统计信息。如果有失败记录，err字段会包含错误提示，import_id用于下载失败数据"
// @Failure 400 {object} ApiResponse "请求参数错误（如文件格式错误、文件过大、包含附件、必填字段缺失、IP地址格式无效、超过1000行限制等）"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 408 {object} ApiResponse "请求超时（文件可能包含大量附件）"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /hosts/csv/import [post]
func ImportHostsFromCSVStream(c *gin.Context) {
	// 设置响应头
	c.Header("Content-Type", "application/json")

	// 添加并发限制，防止同时处理过多导入请求
	select {
	case importSemaphore <- struct{}{}:
		defer func() {
			<-importSemaphore
			// 强制垃圾回收，释放内存
			runtime.GC()
		}()
	default:
		bomb("Too many concurrent import requests. Please try again later.")
	}

	file, err := c.FormFile("file")
	if err != nil {
		bomb("no file uploaded")
	}

	username := headerUsername(c)

	// 检查文件大小限制
	if file.Size > 10*1024*1024 {
		logger.Errorf("File too large - User: %s, FileName: %s, Size: %d",
			username, file.Filename, file.Size)
		bomb("file too large, maximum size is 10MB")
	}

	// 获取流式解析器
	parser, err := getStreamParser(file.Filename)
	if err != nil {
		bomb(err.Error())
		return
	}

	// 创建流式处理器
	processor, err := NewHostStreamProcessor(username)
	if err != nil {
		bomb("Failed to create stream processor: %v", err)
		return
	}
	defer processor.Cleanup()

	// 执行流式解析和处理
	err = parser.ParseStream(file, processor)
	if err != nil {
		bomb(err.Error())
		return
	}

	// 获取处理结果
	successCount, failedCount, totalRows, importId, hasErrors := processor.GetResults()

	if hasErrors {
		response := gin.H{
			"success_count": successCount,
			"failed_count":  failedCount,
			"total_rows":    totalRows,
		}

		if importId != "" {
			response["import_id"] = importId
		}

		logger.Infof("Completed with errors - User: %s, Success: %d, Failed: %d, Total: %d",
			username, successCount, failedCount, totalRows)

		// 判断是部分成功还是完全失败
		// 如果有成功的记录，则视为部分成功，返回成功代码
		// 如果所有记录都失败，则返回错误代码
		var responseCode int
		var errorMessage string
		if successCount > 0 {
			// 部分成功：有一些记录导入成功
			responseCode = CodeSuccess
			errorMessage = "some rows failed to import"
		} else {
			// 完全失败：所有记录都失败
			responseCode = CodeSuccess
			errorMessage = "all rows failed to import"
		}

		c.JSON(200, gin.H{
			"code": responseCode,
			"err":  errorMessage,
			"dat":  response,
		})
		return
	}

	// 全部成功的情况
	logger.Infof("Completed successfully - User: %s, Success: %d, Total: %d",
		username, successCount, totalRows)

	c.JSON(200, gin.H{
		"code": CodeSuccess,
		"err":  "",
		"dat": gin.H{
			"success_count": successCount,
			"failed_count":  0,
			"total_rows":    totalRows,
		},
	})
}

// @Summary 下载导入失败的数据
// @Description 根据导入批次ID下载导入失败的CSV数据，包含原始数据和错误信息
// @Tags 主机管理
// @Accept json
// @Produce application/octet-stream
// @Security ApiKeyAuth
// @Param import_id path string true "导入批次ID"
// @Success 200 {file} file "失败数据CSV文件"
// @Failure 400 {object} ApiResponse "参数错误"
// @Failure 401 {object} ApiResponse "未授权"
// @Failure 404 {object} ApiResponse "导入批次不存在或已过期"
// @Failure 500 {object} ApiResponse "服务器错误"
// @Router /hosts/csv/failed/{import_id} [get]
func DownloadFailedData(c *gin.Context) {
	importId := c.Param("import_id")
	if importId == "" {
		bomb("import_id is required")
	}

	// 清理过期数据
	cleanupExpiredFailedData()

	// 获取失败数据
	failedRows, exists := getFailedData(importId)
	if !exists {
		bomb("import batch not found or expired")
	}

	if len(failedRows) == 0 {
		bomb("no failed data found")
	}

	// 获取统一的头部信息
	headers, err := generateHeaders()
	dangerous(err)

	// 添加错误信息列
	headers = append(headers, "错误信息")

	buf := new(bytes.Buffer)
	// 添加 UTF-8 BOM 以确保中文字符在 Excel 中正确显示
	buf.Write([]byte{0xEF, 0xBB, 0xBF})
	writer := csv.NewWriter(buf)

	if err := writer.Write(headers); err != nil {
		bomb("failed to write CSV headers: %v", err)
	}

	// 写入失败的数据行
	for _, failedRow := range failedRows {
		// 确保数据行有足够的列
		row := make([]string, len(headers))
		for i, data := range failedRow.Data {
			if i < len(headers)-1 { // 最后一列是错误信息
				row[i] = data
			}
		}
		// 添加错误信息到最后一列
		row[len(headers)-1] = failedRow.Error

		if err := writer.Write(row); err != nil {
			bomb("failed to write failed row: %v", err)
		}
	}

	writer.Flush()

	// 设置响应头
	filename := fmt.Sprintf("failed_import_%s.csv", importId[:8])
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s; filename*=UTF-8''%s", filename, filename))
	c.Header("Content-Type", "text/csv; charset=utf-8")
	c.Header("Cache-Control", "no-cache")
	c.Header("Content-Transfer-Encoding", "binary")
	c.Data(http.StatusOK, "application/octet-stream", buf.Bytes())
}

// batchSyncHostsWithConsistency 批量同步主机到JumpServer（保证一致性，支持并发）
func batchSyncHostsWithConsistency(hosts []*models.Host) {
	if len(hosts) == 0 {
		return
	}

	logger.Debugf("Starting concurrent batch sync for %d hosts", len(hosts))
	start := time.Now()

	syncManager, err := service.NewSyncManager()
	if err != nil {
		logger.Errorf("Failed to create sync manager for batch sync: %v", err)
		return
	}

	// 收集有效的主机ID
	validHosts := make([]*models.Host, 0, len(hosts))
	for _, host := range hosts {
		if host.Id > 0 {
			validHosts = append(validHosts, host)
		} else {
			logger.Errorf("Invalid host ID: %d, IP: %s, Ident: %s", host.Id, host.IP, host.Ident)
		}
	}

	if len(validHosts) == 0 {
		logger.Error("No valid hosts found for sync")
		return
	}

	const maxConcurrency = 3 // 限制并发数，避免过多请求导致JumpServer压力过大
	semaphore := make(chan struct{}, maxConcurrency)

	var wg sync.WaitGroup
	var mu sync.Mutex
	successCount := 0
	failedCount := 0

	for _, host := range validHosts {
		wg.Add(1)
		go func(h *models.Host) {
			defer wg.Done()

			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			hostStart := time.Now()
			results, err := syncManager.BatchSyncWithConsistency([]int64{h.Id})

			mu.Lock()
			if err != nil || len(results) == 0 || !results[0].Success {
				failedCount++
				errorMsg := "unknown error"
				if err != nil {
					errorMsg = err.Error()
				} else if len(results) > 0 {
					errorMsg = results[0].Error
				}
				logger.Errorf("Host %d (%s) sync failed in %v: %v", h.Id, h.IP, time.Since(hostStart), errorMsg)
			} else {
				successCount++
				logger.Debugf("Host %d (%s) sync completed in %v", h.Id, h.IP, time.Since(hostStart))
			}
			mu.Unlock()
		}(host)
	}

	wg.Wait()

	logger.Infof("Concurrent batch sync completed in %v: %d success, %d failed, %d total",
		time.Since(start), successCount, failedCount, len(validHosts))
}
