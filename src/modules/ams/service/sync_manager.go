package service

import (
	"fmt"
	"github.com/toolkits/pkg/logger"
	"sync"
	"time"

	"arboris/src/models"
	"arboris/src/modules/ams/jumpserver"
)

// SyncStatus 同步状态
type SyncStatus int

const (
	SyncStatusPending  SyncStatus = iota // 待同步
	SyncStatusSuccess                    // 同步成功
	SyncStatusFailed                     // 同步失败
	SyncStatusRetrying                   // 重试中
)

// SyncRecord 同步记录
type SyncRecord struct {
	ID          int64      `json:"id" xorm:"'id' pk autoincr"`
	HostID      int64      `json:"host_id" xorm:"'host_id'"`
	Operation   string     `json:"operation" xorm:"'operation'"` // create/update/delete
	Status      SyncStatus `json:"status" xorm:"'status'"`
	AssetID     string     `json:"asset_id" xorm:"'asset_id'"`
	NodeID      string     `json:"node_id" xorm:"'node_id'"`
	RetryCount  int        `json:"retry_count" xorm:"'retry_count'"`
	LastError   string     `json:"last_error" xorm:"'last_error'"`
	CreatedAt   time.Time  `json:"created_at" xorm:"'created_at'"`
	UpdatedAt   time.Time  `json:"updated_at" xorm:"'updated_at'"`
	CompletedAt *time.Time `json:"completed_at" xorm:"'completed_at'"`
}

// 全局单例相关变量
var (
	globalSyncManager *SyncManager
	syncManagerOnce   sync.Once
	syncManagerMutex  sync.RWMutex
)

// SyncManager 同步管理器
type SyncManager struct {
	jsService     *JumpServerService
	maxRetry      int
	checkInterval time.Duration
}

// InitGlobalSyncManager 初始化全局同步管理器（在模块启动时调用）
func InitGlobalSyncManager() error {
	var initErr error
	syncManagerOnce.Do(func() {
		jsService, err := NewJumpServerService()
		if err != nil {
			initErr = fmt.Errorf("failed to create JumpServer service: %v", err)
			return
		}

		globalSyncManager = &SyncManager{
			jsService:     jsService,
			maxRetry:      3,                // 最大重试次数
			checkInterval: 15 * time.Minute, // 15分钟检查一次
		}

		logger.Info("Global SyncManager initialized successfully")
	})
	return initErr
}

// GetGlobalSyncManager 获取全局同步管理器实例
func GetGlobalSyncManager() (*SyncManager, error) {
	syncManagerMutex.RLock()
	defer syncManagerMutex.RUnlock()

	if globalSyncManager == nil {
		return nil, fmt.Errorf("global SyncManager not initialized, call InitGlobalSyncManager first")
	}
	return globalSyncManager, nil
}

// ResetGlobalSyncManager 重置全局同步管理器（用于重连或配置更新）
func ResetGlobalSyncManager() error {
	syncManagerMutex.Lock()
	defer syncManagerMutex.Unlock()

	jsService, err := NewJumpServerService()
	if err != nil {
		return fmt.Errorf("failed to create new JumpServer service: %v", err)
	}

	if globalSyncManager != nil {
		// 如果有旧的实例，可以在这里进行清理
		logger.Info("Resetting global SyncManager")
	}

	globalSyncManager = &SyncManager{
		jsService:     jsService,
		maxRetry:      3,
		checkInterval: 15 * time.Minute,
	}

	logger.Info("Global SyncManager reset successfully")
	return nil
}

// GetSyncManagerWithFallback 获取同步管理器，优先使用全局实例，失败时创建新实例
// 这是推荐的获取SyncManager的方式，提供最佳性能和向后兼容性
func GetSyncManagerWithFallback() (*SyncManager, error) {
	// 优先尝试获取全局实例
	if manager, err := GetGlobalSyncManager(); err == nil {
		return manager, nil
	}

	// 如果全局实例不可用，尝试重新初始化
	logger.Warning("Global SyncManager not available, attempting to reinitialize...")
	if err := InitGlobalSyncManager(); err == nil {
		if manager, err := GetGlobalSyncManager(); err == nil {
			return manager, nil
		}
	}

	// 最后的备选方案：创建新实例（性能较差但保证功能可用）
	logger.Warning("Creating new SyncManager instance as fallback (performance impact)")
	return NewSyncManager()
}

// IsGlobalSyncManagerHealthy 检查全局同步管理器是否健康
func IsGlobalSyncManagerHealthy() bool {
	syncManagerMutex.RLock()
	defer syncManagerMutex.RUnlock()

	if globalSyncManager == nil {
		return false
	}

	// 可以添加更多健康检查逻辑，比如测试JumpServer连接
	return true
}

// GetGlobalSyncManagerStats 获取全局同步管理器统计信息
func GetGlobalSyncManagerStats() map[string]interface{} {
	syncManagerMutex.RLock()
	defer syncManagerMutex.RUnlock()

	stats := map[string]interface{}{
		"initialized": globalSyncManager != nil,
		"healthy":     false,
	}

	if globalSyncManager != nil {
		stats["healthy"] = true
		stats["max_retry"] = globalSyncManager.maxRetry
		stats["check_interval"] = globalSyncManager.checkInterval.String()
	}

	return stats
}

// NewSyncManager 创建同步管理器（保持向后兼容，但建议使用全局实例）
// Deprecated: 建议使用 GetGlobalSyncManager() 获取全局实例以提高性能
func NewSyncManager() (*SyncManager, error) {
	// 优先返回全局实例
	if manager, err := GetGlobalSyncManager(); err == nil {
		return manager, nil
	}

	// 如果全局实例不可用，创建新实例（向后兼容）
	logger.Warning("Global SyncManager not available, creating new instance (performance impact)")
	jsService, err := NewJumpServerService()
	if err != nil {
		return nil, err
	}

	return &SyncManager{
		jsService:     jsService,
		maxRetry:      3,                // 最大重试次数
		checkInterval: 15 * time.Minute, // 15分钟检查一次
	}, nil
}

// SyncResult 同步结果
type SyncResult struct {
	HostID    int64  `json:"host_id"`
	Success   bool   `json:"success"`
	Error     string `json:"error,omitempty"`
	AssetID   string `json:"asset_id,omitempty"`
	NodeID    string `json:"node_id,omitempty"`
	Operation string `json:"operation"`
}

// CreateHostWithSync 创建主机并同步
func (sm *SyncManager) CreateHostWithSync(host *models.Host) (*SyncResult, error) {
	result := &SyncResult{
		HostID:    host.Id,
		Operation: "create",
	}

	// 1. 记录同步任务
	syncRecord := &SyncRecord{
		HostID:    host.Id,
		Operation: "create",
		Status:    SyncStatusPending,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
	if err := sm.saveSyncRecord(syncRecord); err != nil {
		result.Error = fmt.Sprintf("failed to save sync record: %v", err)
		return result, err
	}
	// 2. 尝试同步到JumpServer
	err := sm.jsService.SyncHostToJumpServer(host.Id)
	if err != nil {
		// 同步失败，更新记录状态
		syncRecord.Status = SyncStatusFailed
		syncRecord.LastError = err.Error()
		syncRecord.UpdatedAt = time.Now()
		sm.updateSyncRecord(syncRecord)

		result.Error = err.Error()
		logger.Errorf("Host %d sync failed, will retry later: %v", host.Id, err)
		return result, nil // 不返回错误，允许后续重试
	}

	// 3. 同步成功，更新记录
	updatedHost, _ := models.HostGet("id=?", host.Id)
	if updatedHost != nil {
		syncRecord.AssetID = updatedHost.JumpServerAssetID
		syncRecord.NodeID = updatedHost.JumpServerNodeID
	}

	syncRecord.Status = SyncStatusSuccess
	syncRecord.UpdatedAt = time.Now()
	now := time.Now()
	syncRecord.CompletedAt = &now
	sm.updateSyncRecord(syncRecord)

	result.Success = true
	result.AssetID = syncRecord.AssetID
	result.NodeID = syncRecord.NodeID
	logger.Infof("Host %d synced successfully", host.Id)

	return result, nil
}

// UpdateHostWithSync 更新主机并同步
func (sm *SyncManager) UpdateHostWithSync(host *models.Host) (*SyncResult, error) {
	result := &SyncResult{
		HostID:    host.Id,
		Operation: "update",
	}

	// 只有已同步的主机才需要更新
	if host.JumpServerAssetID == "" {
		result.Success = true // 未同步的主机，跳过更新
		return result, nil
	}

	// 记录同步任务
	syncRecord := &SyncRecord{
		HostID:    host.Id,
		Operation: "update",
		Status:    SyncStatusPending,
		AssetID:   host.JumpServerAssetID,
		NodeID:    host.JumpServerNodeID,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	if err := sm.saveSyncRecord(syncRecord); err != nil {
		result.Error = fmt.Sprintf("failed to save sync record: %v", err)
		return result, err
	}

	// 尝试更新JumpServer
	err := sm.jsService.UpdateHostInJumpServer(host)
	if err != nil {
		syncRecord.Status = SyncStatusFailed
		syncRecord.LastError = err.Error()
		syncRecord.UpdatedAt = time.Now()
		sm.updateSyncRecord(syncRecord)

		result.Error = err.Error()
		return result, nil
	}

	// 更新成功
	syncRecord.Status = SyncStatusSuccess
	syncRecord.UpdatedAt = time.Now()
	now := time.Now()
	syncRecord.CompletedAt = &now
	sm.updateSyncRecord(syncRecord)

	result.Success = true
	result.AssetID = syncRecord.AssetID
	result.NodeID = syncRecord.NodeID

	return result, nil
}

// DeleteHostWithSync 删除主机并同步
func (sm *SyncManager) DeleteHostWithSync(host *models.Host) (*SyncResult, error) {
	result := &SyncResult{
		HostID:    host.Id,
		Operation: "delete",
	}

	// 如果主机已同步到JumpServer，需要先删除
	if host.JumpServerAssetID != "" {
		// 记录同步任务
		syncRecord := &SyncRecord{
			HostID:    host.Id,
			Operation: "delete",
			Status:    SyncStatusPending,
			AssetID:   host.JumpServerAssetID,
			NodeID:    host.JumpServerNodeID,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}

		if err := sm.saveSyncRecord(syncRecord); err != nil {
			result.Error = fmt.Sprintf("failed to save sync record: %v", err)
			return result, err
		}

		// 尝试从JumpServer删除
		err := sm.jsService.DeleteHostFromJumpServer(host.JumpServerAssetID)
		if err != nil {
			syncRecord.Status = SyncStatusFailed
			syncRecord.LastError = err.Error()
			syncRecord.UpdatedAt = time.Now()
			sm.updateSyncRecord(syncRecord)

			result.Error = err.Error()
			// 删除操作失败时，不删除AMS中的记录，保持数据一致性
			return result, fmt.Errorf("failed to delete from JumpServer: %v", err)
		}

		// JumpServer删除成功，更新记录
		syncRecord.Status = SyncStatusSuccess
		syncRecord.UpdatedAt = time.Now()
		now := time.Now()
		syncRecord.CompletedAt = &now
		sm.updateSyncRecord(syncRecord)
	}

	result.Success = true
	return result, nil
}

// BatchSyncWithConsistency 批量同步（保证一致性）
func (sm *SyncManager) BatchSyncWithConsistency(hostIDs []int64) ([]SyncResult, error) {
	results := make([]SyncResult, 0, len(hostIDs))

	for _, hostID := range hostIDs {
		host, err := models.HostGet("id=?", hostID)
		if err != nil {
			results = append(results, SyncResult{
				HostID:    hostID,
				Operation: "sync",
				Error:     fmt.Sprintf("host not found: %v", err),
			})
			continue
		}

		if host == nil {
			results = append(results, SyncResult{
				HostID:    hostID,
				Operation: "sync",
				Error:     "host not found",
			})
			continue
		}

		// 根据主机状态决定同步操作
		var result *SyncResult
		if host.JumpServerAssetID == "" {
			// 未同步，执行创建同步
			result, _ = sm.CreateHostWithSync(host)
		} else {
			// 已同步，执行更新同步
			result, _ = sm.UpdateHostWithSync(host)
		}

		if result != nil {
			results = append(results, *result)
		}
	}

	return results, nil
}

// saveSyncRecord 保存同步记录
func (sm *SyncManager) saveSyncRecord(record *SyncRecord) error {
	_, err := models.DB["ams"].Insert(record)
	return err
}

// updateSyncRecord 更新同步记录
func (sm *SyncManager) updateSyncRecord(record *SyncRecord) error {
	_, err := models.DB["ams"].ID(record.ID).Update(record)
	return err
}

// StartConsistencyChecker 启动一致性检查器
func (sm *SyncManager) StartConsistencyChecker() {
	logger.Infof("Starting consistency checker with %v interval", sm.checkInterval)
	go sm.runConsistencyCheck()
}

// runConsistencyCheck 运行一致性检查
func (sm *SyncManager) runConsistencyCheck() {
	ticker := time.NewTicker(sm.checkInterval)
	defer ticker.Stop()

	// 启动后立即执行一次检查
	sm.performConsistencyCheck()

	// 定时执行检查
	for range ticker.C {
		sm.performConsistencyCheck()
	}
}

// performConsistencyCheck 执行一致性检查
func (sm *SyncManager) performConsistencyCheck() {
	logger.Info("Starting consistency check")
	startTime := time.Now()

	// 1. 检查并修复AMS记录（包括未同步和孤立记录）
	repairedCount := sm.checkAndRepairAMSRecords()

	// 2. 检查JumpServer jump-ams节点下的孤立资产（只记录，不处理）
	jumpServerOrphanCount := sm.checkOrphanJumpServerAssets()

	duration := time.Since(startTime)
	logger.Infof("Consistency check completed in %v - repaired %d AMS records, found %d JumpServer orphan assets",
		duration, repairedCount, jumpServerOrphanCount)
}

// checkAndRepairAMSRecords 检查并修复AMS记录（包括未同步和孤立记录）
func (sm *SyncManager) checkAndRepairAMSRecords() int {
	logger.Debug("Checking AMS records for sync issues")

	// 获取所有有JumpServerAssetID的主机记录
	hosts, err := models.HostGetsAll()
	if err != nil {
		logger.Errorf("Failed to get hosts from AMS: %v", err)
		return 0
	}

	repairedCount := 0
	for _, host := range hosts {
		if host.JumpServerAssetID == "" {
			// 未同步的记录，尝试同步到JumpServer
			logger.Warningf("Found unsynced AMS record: host_id=%d, ip=%s, attempting to sync", host.Id, host.IP)

			if sm.syncUnsyncedHost(host) {
				repairedCount++
			}
			continue
		}

		// 检查JumpServer中是否存在对应资产
		exists, err := sm.jsService.client.AssetExists(host.JumpServerAssetID)
		if err != nil {
			logger.Errorf("Failed to check asset %s existence for host %d: %v", host.JumpServerAssetID, host.Id, err)
			continue
		}

		if !exists {
			logger.Warningf("Found orphan AMS record: host_id=%d, asset_id=%s, ip=%s",
				host.Id, host.JumpServerAssetID, host.IP)

			// 尝试修复
			if sm.repairOrphanAMSRecord(host) {
				repairedCount++
			}
		}
	}

	if repairedCount > 0 {
		logger.Infof("Repaired %d AMS records (unsynced + orphan)", repairedCount)
	}

	return repairedCount
}

// repairOrphanAMSRecord 修复AMS中的孤立记录（使用与SyncHostToJumpServer相同的逻辑）
func (sm *SyncManager) repairOrphanAMSRecord(host *models.Host) bool {
	logger.Infof("Attempting to repair orphan AMS record: host_id=%d, ip=%s, idc=%s", host.Id, host.IP, host.IDC)

	// 先清除无效的JumpServerAssetID
	updateData := map[string]interface{}{
		"jumpserver_asset_id": "",
	}
	if updateErr := host.Update(updateData); updateErr != nil {
		logger.Errorf("Failed to clear invalid asset ID for host %d: %v", host.Id, updateErr)
		return false
	}

	// 使用JumpServerService的SyncHostToJumpServer方法重新同步
	err := sm.jsService.SyncHostToJumpServer(host.Id)
	if err != nil {
		logger.Errorf("Failed to repair orphan record for host %d: %v", host.Id, err)
		return false
	}

	logger.Infof("Successfully repaired orphan AMS record: host_id=%d", host.Id)
	return true
}

// syncUnsyncedHost 同步未同步的主机到JumpServer（使用与SyncHostToJumpServer相同的逻辑）
func (sm *SyncManager) syncUnsyncedHost(host *models.Host) bool {
	logger.Infof("Attempting to sync unsynced host: host_id=%d, ip=%s, idc=%s", host.Id, host.IP, host.IDC)

	// 使用JumpServerService的SyncHostToJumpServer方法，保持逻辑一致
	err := sm.jsService.SyncHostToJumpServer(host.Id)
	if err != nil {
		logger.Errorf("Failed to sync host %d to JumpServer: %v", host.Id, err)
		return false
	}

	logger.Infof("Successfully synced unsynced host: host_id=%d", host.Id)
	return true
}

// checkOrphanJumpServerAssets 检查JumpServer中的孤立资产（只记录，不处理）
func (sm *SyncManager) checkOrphanJumpServerAssets() int {
	logger.Debug("Checking orphan JumpServer assets")

	// 获取jump-ams节点下的所有资产
	jumpAMSAssets, err := sm.jsService.client.GetJumpAMSNodeAssets()
	if err != nil {
		logger.Errorf("Failed to get jump-ams assets: %v", err)
		return 0
	}

	orphanCount := 0
	for _, asset := range jumpAMSAssets {
		// 检查AMS中是否有对应记录
		host, err := models.HostGetByJumpServerAssetID(asset.ID)
		if err != nil {
			logger.Errorf("Failed to query AMS for asset %s: %v", asset.ID, err)
			continue
		}

		if host == nil {
			logger.Warningf("Found orphan JumpServer asset: asset_id=%s, name=%s, ip=%s",
				asset.ID, asset.Name, asset.Address)
			orphanCount++

			// 只记录，不自动处理，避免误操作
			sm.logOrphanJumpServerAsset(asset)
		}
	}

	if orphanCount > 0 {
		logger.Warningf("Found %d orphan JumpServer assets in jump-ams node", orphanCount)
	}

	return orphanCount
}

// logOrphanJumpServerAsset 记录JumpServer孤立资产信息
func (sm *SyncManager) logOrphanJumpServerAsset(asset jumpserver.UserAsset) {
	// 这里可以将孤立资产信息记录到数据库或发送告警
	// 暂时只记录到日志
	logger.Warningf("Orphan JumpServer asset details - ID: %s, Name: %s, Address: %s, Platform: %s, CreatedBy: %s, DateCreated: %s",
		asset.ID, asset.Name, asset.Address, asset.Platform.Name, asset.CreatedBy, asset.DateCreated)
}
