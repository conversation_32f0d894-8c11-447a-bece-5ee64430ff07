package service

import (
	"fmt"
	"github.com/toolkits/pkg/logger"
	"time"

	"arboris/src/models"
	"arboris/src/modules/ams/config"
	"arboris/src/modules/ams/jumpserver"
)

// JumpServerService JumpServer服务
type JumpServerService struct {
	client *jumpserver.Client
}

// NewJumpServerService 创建JumpServer服务
// Deprecated: 建议使用 GetJumpServerServiceWithFallback() 获取全局实例以提高性能
func NewJumpServerService() (*JumpServerService, error) {
	logger.Debug("Creating new JumpServerService instance (consider using global instance for better performance)")
	cfg := config.Config.JumpServer

	timeout := time.Duration(cfg.Timeout) * time.Second
	if timeout == 0 {
		timeout = 30 * time.Second
	}

	client := jumpserver.NewClient(
		cfg.BaseURL,
		cfg.Username,
		cfg.Password,
		cfg.Token,
		cfg.Organization,
		timeout,
	)

	// 测试连接
	if err := client.TestConnection(); err != nil {
		return nil, fmt.Errorf("failed to connect to JumpServer: %v", err)
	}

	return &JumpServerService{
		client: client,
	}, nil
}

// CreateUser 创建用户
func (s *JumpServerService) CreateUser(username, name, email, phone string) (*jumpserver.User, error) {
	req := &jumpserver.UserCreateRequest{
		Username: username,
		Name:     name,
		Email:    email,
		Phone:    phone,
		IsActive: true,
	}

	return s.client.CreateUser(req)
}

// CreateUserWithPassword 创建用户并生成随机密码
func (s *JumpServerService) CreateUserWithPassword(username, name, email, phone string) (*jumpserver.UserCreateResponse, error) {
	req := &jumpserver.UserCreateRequest{
		Username: username,
		Name:     name,
		Email:    email,
		Phone:    phone,
		IsActive: true,
	}

	return s.client.CreateUserWithPassword(req)
}

// DeleteUser 删除用户
func (s *JumpServerService) DeleteUser(username string) error {
	user, err := s.client.GetUser(username)
	if err != nil {
		return err
	}

	return s.client.DeleteUser(user.ID)
}

// DisableUser 禁用用户
func (s *JumpServerService) DisableUser(username string) (*jumpserver.User, error) {
	user, err := s.client.GetUser(username)
	if err != nil {
		return nil, err
	}

	return s.client.UpdateUserStatus(user.ID, false)
}

// EnableUser 激活用户
func (s *JumpServerService) EnableUser(username string) (*jumpserver.User, error) {
	user, err := s.client.GetUser(username)
	if err != nil {
		return nil, err
	}

	return s.client.UpdateUserStatus(user.ID, true)
}

// GetUserHosts 获取用户有权限的主机列表
func (s *JumpServerService) GetUserHosts(username string) ([]*models.Host, error) {
	// 从JumpServer获取用户可访问的资产
	assets, err := s.client.GetUserPermissions(username)
	if err != nil {
		return nil, err
	}

	// 收集所有有权限的资产ID
	assetIDMap := make(map[string]bool)
	for _, asset := range assets {
		assetIDMap[asset.ID] = true
	}

	// 根据JumpServer资产ID查询AMS主机信息
	var hosts []*models.Host
	for assetID := range assetIDMap {
		host, err := models.HostGetByJumpServerAssetID(assetID)
		if err != nil {
			logger.Errorf("Failed to get host by asset ID %s: %v", assetID, err)
			continue
		}
		if host != nil {
			hosts = append(hosts, host)
		}
	}

	return hosts, nil
}

// SyncHostToJumpServer 同步主机到JumpServer
func (s *JumpServerService) SyncHostToJumpServer(hostID int64) error {
	host, err := models.HostGet("id=?", hostID)
	if err != nil {
		return err
	}

	if host == nil {
		return fmt.Errorf("host not found: %d", hostID)
	}

	// 确保根节点存在
	rootNode, err := s.ensureJumpAMSRootNode()
	if err != nil {
		return err
	}

	// 根据IDC字段确定目标节点
	var targetNode *jumpserver.Node
	if host.IDC != "" {
		// 有IDC字段，创建或获取IDC子节点
		idcNode, err := s.client.GetOrCreateIDCNode(host.IDC, rootNode.ID)
		if err != nil {
			return fmt.Errorf("failed to create/get IDC node '%s': %v", host.IDC, err)
		}
		targetNode = idcNode
	} else {
		// 没有IDC字段，使用根节点
		targetNode = rootNode
	}

	// 同步到目标节点
	asset, err := s.syncHostToJumpServer(host, targetNode.ID)
	if err != nil {
		return err
	}

	// 更新主机的JumpServer信息
	return models.HostUpdateJumpServerInfo(hostID, asset.ID, targetNode.ID, "")
}

// DeleteHostFromJumpServer 从JumpServer中删除主机
func (s *JumpServerService) DeleteHostFromJumpServer(assetID string) error {
	if assetID == "" {
		return nil // 如果没有资产ID，说明没有同步过，直接返回
	}

	// 删除JumpServer中的资产
	return s.client.DeleteAsset(assetID)
}

// UpdateHostInJumpServer 更新JumpServer中的主机信息
func (s *JumpServerService) UpdateHostInJumpServer(host *models.Host) error {
	if host.JumpServerAssetID == "" {
		return nil // 如果没有资产ID，说明没有同步过，直接返回
	}

	// 构建更新的资产信息
	asset := &jumpserver.Asset{
		ID:       host.JumpServerAssetID,
		Hostname: host.Name,
		IP:       host.IP,
		Platform: config.Config.JumpServer.AssetConfig.DefaultPlatform,
		Protocols: []jumpserver.Protocol{
			{Name: config.Config.JumpServer.AssetConfig.DefaultProtocol, Port: config.Config.JumpServer.AssetConfig.DefaultPort},
		},
		IsActive: true,
		Comment:  host.Note,
		Labels: map[string]string{
			"ams_id":    fmt.Sprintf("%d", host.Id),
			"ams_ident": host.Ident,
			"category":  host.Cate,
		},
	}

	// 更新JumpServer中的资产
	return s.client.UpdateAsset(asset)
}

// syncHostToJumpServer 内部方法：同步主机到JumpServer
func (s *JumpServerService) syncHostToJumpServer(host *models.Host, nodeID string) (*jumpserver.Asset, error) {
	// 构建JumpServer资产
	asset := &jumpserver.Asset{
		Hostname: host.Name,
		IP:       host.IP,
		Platform: config.Config.JumpServer.AssetConfig.DefaultPlatform,
		Protocols: []jumpserver.Protocol{
			{Name: config.Config.JumpServer.AssetConfig.DefaultProtocol, Port: config.Config.JumpServer.AssetConfig.DefaultPort},
		},
		IsActive: true,
		Comment:  host.Note,
		Labels: map[string]string{
			"ams_id":    fmt.Sprintf("%d", host.Id),
			"ams_ident": host.Ident,
			"category":  host.Cate,
			"idc":       host.IDC,
		},
		Nodes: []string{nodeID},
	}

	return s.client.CreateAsset(asset)
}

// ensureJumpAMSRootNode 确保根节点存在
func (s *JumpServerService) ensureJumpAMSRootNode() (*jumpserver.Node, error) {
	rootNodeName := config.Config.JumpServer.NodeConfig.RootNodeName

	// 查找根节点
	rootNode, err := s.client.GetNodeByValue(rootNodeName)
	if err == nil {
		return rootNode, nil
	}
	logger.Errorf("ensureJumpAMSRootNode err:%s", err.Error())

	// 不存在则创建
	return s.client.CreateNode(rootNodeName, rootNodeName, "")
}

// GrantUserPermissionsByUsernameAndIPs 根据用户名和IP列表给用户授权
func (s *JumpServerService) GrantUserPermissionsByUsernameAndIPs(username string, hostIPs []string) (*UserPermissionGrantResult, error) {
	result := &UserPermissionGrantResult{
		Username:       username,
		PermissionName: fmt.Sprintf("%s%s", config.Config.JumpServer.PermConfig.PermissionPrefix, username),
		TotalHosts:     len(hostIPs),
		SuccessHosts:   make([]string, 0),
		FailedHosts:    make([]HostPermissionError, 0),
	}

	// 1. 检查用户是否存在
	user, err := s.client.GetUser(username)
	if err != nil {
		return nil, fmt.Errorf("failed to get user %s: %v", username, err)
	}
	if user == nil {
		return nil, fmt.Errorf("user %s not found", username)
	}

	// 2. 根据IP查找对应的主机和资产ID
	var assetIDs []string
	for _, hostIP := range hostIPs {
		// 从AMS数据库中查找主机
		host, err := models.HostGet("ip=?", hostIP)
		if err != nil {
			result.FailedHosts = append(result.FailedHosts, HostPermissionError{
				HostIP: hostIP,
				Error:  fmt.Sprintf("failed to find host in AMS: %v", err),
			})
			continue
		}
		if host == nil {
			result.FailedHosts = append(result.FailedHosts, HostPermissionError{
				HostIP: hostIP,
				Error:  "host not found in AMS",
			})
			continue
		}

		// 如果主机还没有同步到JumpServer，先同步
		if host.JumpServerAssetID == "" {
			if err := s.SyncHostToJumpServer(host.Id); err != nil {
				result.FailedHosts = append(result.FailedHosts, HostPermissionError{
					HostIP: hostIP,
					Error:  fmt.Sprintf("failed to sync host to JumpServer: %v", err),
				})
				continue
			}
			// 重新查询获取同步后的资产ID
			host, _ = models.HostGet("ip=?", hostIP)
		}

		if host.JumpServerAssetID != "" {
			assetIDs = append(assetIDs, host.JumpServerAssetID)
			result.SuccessHosts = append(result.SuccessHosts, hostIP)
		} else {
			result.FailedHosts = append(result.FailedHosts, HostPermissionError{
				HostIP: hostIP,
				Error:  "failed to get JumpServer asset ID after sync",
			})
		}
	}

	// 3. 创建或更新权限
	if len(assetIDs) > 0 {
		permission := &jumpserver.Permission{
			Name:        result.PermissionName,
			Users:       []string{user.ID}, // 直接给用户授权
			Assets:      assetIDs,
			Accounts:    []string{"@ALL"},    // 授权全部账号
			Protocols:   []string{"all"},     // 授权全部协议
			Actions:     getDefaultActions(), // 添加默认动作
			IsActive:    true,
			DateExpired: "", // 永不过期
		}

		// 尝试创建权限，如果已存在则更新
		if err := s.client.CreateOrUpdateUserPermission(user.ID, permission); err != nil {
			// 如果创建失败，将所有成功的主机标记为失败
			for _, hostIP := range result.SuccessHosts {
				result.FailedHosts = append(result.FailedHosts, HostPermissionError{
					HostIP: hostIP,
					Error:  fmt.Sprintf("failed to create permission: %v", err),
				})
			}
			result.SuccessHosts = make([]string, 0)
		}
	}

	// 更新统计
	result.SuccessCount = len(result.SuccessHosts)
	result.FailedCount = len(result.FailedHosts)

	return result, nil
}

// RevokeUserPermissionsByUsernameAndIPs 根据用户名和IP列表回收用户权限
func (s *JumpServerService) RevokeUserPermissionsByUsernameAndIPs(username string, hostIPs []string) (*UserPermissionRevokeResult, error) {
	result := &UserPermissionRevokeResult{
		Username:     username,
		TotalHosts:   len(hostIPs),
		SuccessHosts: make([]string, 0),
		FailedHosts:  make([]HostPermissionError, 0),
	}

	// 1. 检查用户是否存在
	user, err := s.client.GetUser(username)
	if err != nil {
		return nil, fmt.Errorf("failed to get user %s: %v", username, err)
	}
	if user == nil {
		return nil, fmt.Errorf("user %s not found", username)
	}

	permissionName := fmt.Sprintf("%s%s", config.Config.JumpServer.PermConfig.PermissionPrefix, username)

	// 2. 如果没有提供IP列表，则删除整个权限规则
	if len(hostIPs) == 0 {
		logger.Infof("No host IPs provided, deleting entire permission rule for user %s", username)

		if err := s.client.DeletePermissionByName(permissionName); err != nil {
			return nil, fmt.Errorf("failed to delete permission %s: %v", permissionName, err)
		}

		// 设置成功结果（没有具体的主机IP，所以success_hosts为空）
		result.SuccessCount = 1 // 表示成功删除了权限规则
		result.FailedCount = 0
		result.TotalHosts = 0

		return result, nil
	}

	// 3. 如果提供了IP列表，则只回收指定主机的权限
	var assetIDs []string
	for _, hostIP := range hostIPs {
		host, err := models.HostGet("ip=?", hostIP)
		if err != nil {
			result.FailedHosts = append(result.FailedHosts, HostPermissionError{
				HostIP: hostIP,
				Error:  fmt.Sprintf("failed to find host in AMS: %v", err),
			})
			continue
		}
		if host == nil {
			result.FailedHosts = append(result.FailedHosts, HostPermissionError{
				HostIP: hostIP,
				Error:  "host not found in AMS",
			})
			continue
		}

		if host.JumpServerAssetID != "" {
			assetIDs = append(assetIDs, host.JumpServerAssetID)
			result.SuccessHosts = append(result.SuccessHosts, hostIP)
		} else {
			result.FailedHosts = append(result.FailedHosts, HostPermissionError{
				HostIP: hostIP,
				Error:  "host not synced to JumpServer",
			})
		}
	}

	// 4. 回收指定资产的权限
	if len(assetIDs) > 0 {
		logger.Infof("assetIDs: %v", assetIDs)
		if err := s.client.RevokeUserPermissionByAssets(user.ID, permissionName, assetIDs); err != nil {
			// 如果回收失败，将所有成功的主机标记为失败
			for _, hostIP := range result.SuccessHosts {
				result.FailedHosts = append(result.FailedHosts, HostPermissionError{
					HostIP: hostIP,
					Error:  fmt.Sprintf("failed to revoke permission: %v", err),
				})
			}
			result.SuccessHosts = make([]string, 0)
		}
	}

	// 更新统计
	result.SuccessCount = len(result.SuccessHosts)
	result.FailedCount = len(result.FailedHosts)

	return result, nil
}

// UserPermissionGrantResult 用户权限授权结果
type UserPermissionGrantResult struct {
	Username       string                `json:"username"`
	PermissionName string                `json:"permission_name"`
	SuccessHosts   []string              `json:"success_hosts"`
	FailedHosts    []HostPermissionError `json:"failed_hosts"`
	TotalHosts     int                   `json:"total_hosts"`
	SuccessCount   int                   `json:"success_count"`
	FailedCount    int                   `json:"failed_count"`
}

// UserPermissionRevokeResult 用户权限回收结果
type UserPermissionRevokeResult struct {
	Username     string                `json:"username"`
	SuccessHosts []string              `json:"success_hosts"`
	FailedHosts  []HostPermissionError `json:"failed_hosts"`
	TotalHosts   int                   `json:"total_hosts"`
	SuccessCount int                   `json:"success_count"`
	FailedCount  int                   `json:"failed_count"`
}

// HostPermissionError 主机权限操作错误
type HostPermissionError struct {
	HostIP string `json:"host_ip"`
	Error  string `json:"error"`
}

// getDefaultActions 获取默认的权限动作
func getDefaultActions() []map[string]interface{} {
	return []map[string]interface{}{
		{"value": "connect", "label": "连接 (所有协议)"},
		{"value": "upload", "label": "上传 (RDP, SFTP)"},
		{"value": "download", "label": "下载 (RDP, SFTP)"},
		{"value": "copy", "label": "复制 (RDP, VNC)"},
		{"value": "paste", "label": "粘贴 (RDP, VNC)"},
		{"value": "delete", "label": "删除 (SFTP)"},
		{"value": "share", "label": "分享 (SSH)"},
	}
}

// RevokeUserPermissions 回收单个用户的权限
func (s *JumpServerService) RevokeUserPermissions(username string, hostIDs []int64) error {
	// 1. 获取用户信息
	_, err := s.client.GetUser(username)
	if err != nil {
		return fmt.Errorf("user not found: %s", err)
	}

	// 2. 获取所有权限
	permissions, err := s.client.GetPermissions()
	if err != nil {
		return fmt.Errorf("failed to get permissions: %v", err)
	}

	// 3. 收集需要回收的资产ID
	var targetAssetIDs []string
	for _, hostID := range hostIDs {
		host, err := models.HostGet("id=?", hostID)
		if err != nil {
			logger.Errorf("Failed to get host %d: %v", hostID, err)
			continue
		}
		if host != nil && host.JumpServerAssetID != "" {
			targetAssetIDs = append(targetAssetIDs, host.JumpServerAssetID)
		}
	}

	// 4. 查找并处理相关权限
	for _, perm := range permissions {
		// 检查权限是否包含目标用户（通过用户组）
		userMatched := false
		for range perm.UserGroups {
			// 这里需要检查用户是否在该组中，简化处理，可以通过用户组名称判断
			// 在实际应用中，可能需要更精确的用户组成员检查
			userMatched = true // 简化处理
			break
		}

		if !userMatched {
			continue
		}

		// 检查权限是否包含目标资产
		assetMatched := false
		for _, assetID := range perm.Assets {
			for _, targetAssetID := range targetAssetIDs {
				if assetID == targetAssetID {
					assetMatched = true
					break
				}
			}
			if assetMatched {
				break
			}
		}

		if assetMatched {
			logger.Errorf("Found permission %s affecting user %s", perm.ID, username)
			// 这里可以选择删除整个权限或者从权限中移除特定资产
			// 为了简化，我们记录日志，具体处理逻辑可以根据需求调整
		}
	}

	return nil
}
