// Package docs_ams Code generated by swaggo/swag. DO NOT EDIT
package docs_ams

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "termsOfService": "http://swagger.io/terms/",
        "contact": {
            "name": "API Support",
            "url": "http://www.swagger.io/support",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "Apache 2.0",
            "url": "http://www.apache.org/licenses/LICENSE-2.0.html"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/host": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "添加单台主机，支持基础字段和自定义字段。注意：IP、标识符(ident)和主机名(name)必须唯一，与JumpServer保持一致",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "主机管理"
                ],
                "summary": "添加主机",
                "parameters": [
                    {
                        "description": "主机信息",
                        "name": "host",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.hostAddForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "添加成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/models.Host"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误（包括IP、ident或name重复）",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/host/{id}": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "根据主机ID获取主机详细信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "主机管理"
                ],
                "summary": "获取单个主机信息",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "主机ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "主机信息",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/models.Host"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "主机不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "更新单台主机的详细信息，支持基础字段。IP、ID和标识符不可修改，主机名(name)必须唯一",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "主机管理"
                ],
                "summary": "更新主机信息",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "主机ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "主机更新参数",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.hostUpdateForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新后的主机信息",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/models.Host"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "参数错误（包括name重复）",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "主机不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/host/{id}/fields": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "获取指定主机的所有自定义字段值",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "主机字段管理"
                ],
                "summary": "获取主机字段值",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "主机ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "字段值列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/models.HostFieldValue"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "批量更新指定主机的自定义字段值",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "主机字段管理"
                ],
                "summary": "批量更新主机字段值",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "主机ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "字段值列表",
                        "name": "fields",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/models.HostFieldValue"
                            }
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/hosts": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "获取主机列表，支持分页和多字段查询。支持单个查询和批量查询（多个值用逗号分隔）",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "主机管理"
                ],
                "summary": "获取主机列表",
                "parameters": [
                    {
                        "type": "integer",
                        "default": 20,
                        "description": "每页数量",
                        "name": "limit",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "页码",
                        "name": "p",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"1,2,3\"",
                        "description": "主机ID查询，支持批量查询，多个值用逗号分隔",
                        "name": "id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"SN001,SN002\"",
                        "description": "序列号查询，支持批量查询，多个值用逗号分隔",
                        "name": "sn",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"***********,***********\"",
                        "description": "IP地址查询，支持批量查询，多个值用逗号分隔",
                        "name": "ip",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"host-001,host-002\"",
                        "description": "标识符查询，支持批量查询，多个值用逗号分隔",
                        "name": "ident",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"web-server,db-server\"",
                        "description": "主机名查询，支持批量查询，多个值用逗号分隔",
                        "name": "name",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"Ubuntu 20.04,CentOS 7\"",
                        "description": "操作系统版本查询，支持批量查询，多个值用逗号分隔",
                        "name": "os_version",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"5.4.0,3.10.0\"",
                        "description": "内核版本查询，支持批量查询，多个值用逗号分隔",
                        "name": "kernel_version",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"Intel Xeon,AMD EPYC\"",
                        "description": "CPU型号查询，支持批量查询，多个值用逗号分隔",
                        "name": "cpu_model",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"NVIDIA Tesla V100,NVIDIA RTX 3080\"",
                        "description": "GPU型号查询，支持批量查询，多个值用逗号分隔",
                        "name": "gpu_model",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"us-west-1a,us-west-1b\"",
                        "description": "可用区查询，支持批量查询，多个值用逗号分隔",
                        "name": "zone",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"rack-01,rack-02\"",
                        "description": "机架查询，支持批量查询，多个值用逗号分隔",
                        "name": "rack",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"production,testing\"",
                        "description": "备注查询，支持批量查询，多个值用逗号分隔",
                        "name": "note",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"Dell R740,HP DL380\"",
                        "description": "型号查询，支持批量查询，多个值用逗号分隔",
                        "name": "model",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"Dell,HP,Lenovo\"",
                        "description": "厂商查询，支持批量查询，多个值用逗号分隔",
                        "name": "manufacturer",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"beijing,shanghai\"",
                        "description": "IDC机房查询，支持批量查询，多个值用逗号分隔",
                        "name": "idc",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"1,2,4\"",
                        "description": "GPU卡数查询，支持批量查询，多个值用逗号分隔",
                        "name": "gpu",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"8,16,32\"",
                        "description": "CPU核数查询，支持批量查询，多个值用逗号分隔",
                        "name": "cpu",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"16GB,32GB,64GB\"",
                        "description": "内存查询，支持批量查询，多个值用逗号分隔",
                        "name": "mem",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"500GB,1TB,2TB\"",
                        "description": "磁盘查询，支持批量查询，多个值用逗号分隔",
                        "name": "disk",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"host,vm,container\"",
                        "description": "类别查询，支持批量查询，多个值用逗号分隔",
                        "name": "cate",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "example": "\"team-a,team-b\"",
                        "description": "租户查询，支持批量查询，多个值用逗号分隔",
                        "name": "tenant",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "主机列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/http.HostListResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "批量删除主机设备，支持通过ID或IP地址删除",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "主机管理"
                ],
                "summary": "批量删除主机",
                "parameters": [
                    {
                        "description": "主机ID或IP列表",
                        "name": "hosts",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.idsOrIpsForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/hosts/cate": {
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "批量修改主机设备的类别信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "主机管理"
                ],
                "summary": "批量修改主机类别",
                "parameters": [
                    {
                        "description": "类别信息",
                        "name": "cate",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.hostCateForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "修改成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/hosts/csv/failed/{import_id}": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "根据导入批次ID下载导入失败的CSV数据，包含原始数据和错误信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/octet-stream"
                ],
                "tags": [
                    "主机管理"
                ],
                "summary": "下载导入失败的数据",
                "parameters": [
                    {
                        "type": "string",
                        "description": "导入批次ID",
                        "name": "import_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "失败数据CSV文件",
                        "schema": {
                            "type": "file"
                        }
                    },
                    "400": {
                        "description": "参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "导入批次不存在或已过期",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/hosts/csv/import": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "通过CSV或Excel文件批量导入主机数据，支持基础字段和自定义字段。支持.csv、.xlsx格式。最大支持1000行数据导入，文件大小限制10MB。必填字段：IP地址、标识符、主机名、类别。IP地址格式校验：必须是有效的IPv4或IPv6地址，不允许0.0.0.0和***************。注意：不支持包含大量附件（图片、嵌入文档等）的Excel文件，请移除附件后重试。返回导入结果统计信息，详细错误信息可通过失败数据下载接口获取。成功导入的主机将自动异步同步到JumpServer，同步过程不影响导入操作的响应",
                "consumes": [
                    "multipart/form-data"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "主机管理"
                ],
                "summary": "批量导入主机",
                "parameters": [
                    {
                        "type": "file",
                        "description": "CSV或Excel文件（支持.csv、.xlsx格式，最大1000行数据，文件大小限制10MB，不支持包含附件的文件）。必填字段：IP地址、标识符、主机名、类别。IP地址必须是有效格式，如***********00",
                        "name": "file",
                        "in": "formData",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "导入完成，返回统计信息。如果有失败记录，err字段会包含错误提示，import_id用于下载失败数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/http.ImportResult"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误（如文件格式错误、文件过大、包含附件、必填字段缺失、IP地址格式无效、超过1000行限制等）",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "408": {
                        "description": "请求超时（文件可能包含大量附件）",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/hosts/csv/template": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "动态根据自定义字段生成主机导入模板文件，支持CSV和Excel格式",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/octet-stream"
                ],
                "tags": [
                    "主机管理"
                ],
                "summary": "下载主机导入模板",
                "parameters": [
                    {
                        "enum": [
                            "csv",
                            "xlsx"
                        ],
                        "type": "string",
                        "default": "csv",
                        "description": "文件格式",
                        "name": "format",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "模板文件",
                        "schema": {
                            "type": "file"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/hosts/field/{id}": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "根据字段ID获取主机字段详细信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "主机字段管理"
                ],
                "summary": "获取单个主机字段",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "字段ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "字段信息",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/models.HostField"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "字段不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "更新主机自定义字段信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "主机字段管理"
                ],
                "summary": "更新主机字段",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "字段ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "字段信息",
                        "name": "field",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.HostField"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "更新成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "delete": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "删除主机自定义字段",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "主机字段管理"
                ],
                "summary": "删除主机字段",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "字段ID",
                        "name": "id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "字段不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/hosts/fields": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "获取所有主机自定义字段列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "主机字段管理"
                ],
                "summary": "获取主机字段列表",
                "responses": {
                    "200": {
                        "description": "字段列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "array",
                                            "items": {
                                                "$ref": "#/definitions/models.HostField"
                                            }
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            },
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "创建新的主机自定义字段",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "主机字段管理"
                ],
                "summary": "创建主机字段",
                "parameters": [
                    {
                        "description": "字段信息",
                        "name": "field",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/models.HostField"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "创建成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/hosts/fields/values": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "获取多个主机的指定自定义字段值",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "主机字段管理"
                ],
                "summary": "批量获取主机字段值",
                "parameters": [
                    {
                        "description": "查询条件",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.hostFieldValueForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "字段值数据",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "object",
                                            "additionalProperties": true
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/hosts/note": {
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "批量修改主机设备的备注信息",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "主机管理"
                ],
                "summary": "批量修改主机备注",
                "parameters": [
                    {
                        "description": "备注信息",
                        "name": "note",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.hostNoteForm"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "修改成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/hosts/sync": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "将AMS中的主机同步到JumpServer的jump-ams节点下",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "主机管理"
                ],
                "summary": "同步主机到JumpServer",
                "parameters": [
                    {
                        "description": "同步主机信息",
                        "name": "sync",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.HostSyncRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "同步成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/permissions/grant-user": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "批量给用户更新机器授权(有则更新，无则新建)，使用用户名和IP地址作为参数。授权名称格式为\"perf-用户名\"。自动授权全部协议和全部账号",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "权限管理"
                ],
                "summary": "批量给用户更新机器授权",
                "parameters": [
                    {
                        "description": "用户权限授权信息",
                        "name": "permission",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.UserPermissionGrantRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "授权结果",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/http.UserPermissionGrantResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/permissions/revoke-user": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "回收指定用户的访问权限。如果提供host_ips则只回收指定机器的权限，如果不提供host_ips则删除整个权限规则",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "权限管理"
                ],
                "summary": "回收用户权限",
                "parameters": [
                    {
                        "description": "用户权限回收信息",
                        "name": "permission",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.UserPermissionRevokeRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "回收结果",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/http.UserPermissionRevokeResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/ping": {
            "get": {
                "description": "检查服务器运行状态",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "系统"
                ],
                "summary": "健康检查",
                "responses": {
                    "200": {
                        "description": "pong",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "string"
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        },
        "/sync/reset": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "重置全局同步管理器，重新建立JumpServer连接。用于配置更新后的重连",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "系统管理"
                ],
                "summary": "重置同步管理器",
                "responses": {
                    "200": {
                        "description": "重置成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/sync/status": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "获取全局同步管理器的状态和统计信息，用于监控和调试",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "系统管理"
                ],
                "summary": "获取同步管理器状态",
                "responses": {
                    "200": {
                        "description": "同步管理器状态信息",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "type": "object",
                                            "additionalProperties": true
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/users": {
            "post": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "创建用户并同步到JumpServer，自动生成随机密码",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户管理"
                ],
                "summary": "创建用户",
                "parameters": [
                    {
                        "description": "用户信息",
                        "name": "user",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.UserCreateRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "创建成功，返回用户信息和生成的密码",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/http.UserCreateResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/users/{username}": {
            "delete": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "删除用户（从JumpServer中删除）",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户管理"
                ],
                "summary": "删除用户",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户名",
                        "name": "username",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "删除成功",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "用户不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/users/{username}/disable": {
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "禁用用户（在JumpServer中设置为非激活状态）",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户管理"
                ],
                "summary": "禁用用户",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户名",
                        "name": "username",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "禁用成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/http.UserStatusResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "用户不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/users/{username}/enable": {
            "put": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "激活用户（在JumpServer中设置为激活状态）",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户管理"
                ],
                "summary": "激活用户",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户名",
                        "name": "username",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "激活成功",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/http.UserStatusResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "用户不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        },
        "/users/{username}/hosts": {
            "get": {
                "security": [
                    {
                        "ApiKeyAuth": []
                    }
                ],
                "description": "查询用户在JumpServer中有权限访问的主机列表",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "用户管理"
                ],
                "summary": "查询用户权限的主机列表",
                "parameters": [
                    {
                        "type": "string",
                        "description": "用户名",
                        "name": "username",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "用户主机权限列表",
                        "schema": {
                            "allOf": [
                                {
                                    "$ref": "#/definitions/http.ApiResponse"
                                },
                                {
                                    "type": "object",
                                    "properties": {
                                        "dat": {
                                            "$ref": "#/definitions/http.UserHostsResponse"
                                        }
                                    }
                                }
                            ]
                        }
                    },
                    "400": {
                        "description": "请求参数错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "401": {
                        "description": "未授权",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "404": {
                        "description": "用户不存在",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    },
                    "500": {
                        "description": "服务器错误",
                        "schema": {
                            "$ref": "#/definitions/http.ApiResponse"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "http.ApiResponse": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "integer"
                },
                "dat": {},
                "err": {
                    "type": "string"
                }
            }
        },
        "http.HostListResponse": {
            "type": "object",
            "properties": {
                "list": {
                    "type": "array",
                    "items": {}
                },
                "total": {
                    "type": "integer"
                }
            }
        },
        "http.HostPermissionError": {
            "type": "object",
            "properties": {
                "error": {
                    "description": "错误信息",
                    "type": "string"
                },
                "host_ip": {
                    "description": "主机IP",
                    "type": "string"
                }
            }
        },
        "http.HostSyncRequest": {
            "type": "object",
            "required": [
                "host_ids"
            ],
            "properties": {
                "host_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "http.ImportResult": {
            "type": "object",
            "properties": {
                "failed_count": {
                    "type": "integer"
                },
                "import_id": {
                    "type": "string"
                },
                "success_count": {
                    "type": "integer"
                },
                "total_rows": {
                    "type": "integer"
                }
            }
        },
        "http.UserCreateRequest": {
            "type": "object",
            "required": [
                "display_name",
                "email",
                "username"
            ],
            "properties": {
                "display_name": {
                    "type": "string"
                },
                "email": {
                    "type": "string"
                },
                "phone": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "http.UserCreateResponse": {
            "type": "object",
            "properties": {
                "email": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "is_active": {
                    "type": "boolean"
                },
                "name": {
                    "type": "string"
                },
                "password": {
                    "description": "添加密码字段",
                    "type": "string"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "http.UserHostsResponse": {
            "type": "object",
            "properties": {
                "hosts": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/models.Host"
                    }
                },
                "total": {
                    "type": "integer"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "http.UserPermissionGrantRequest": {
            "type": "object",
            "required": [
                "host_ips",
                "username"
            ],
            "properties": {
                "host_ips": {
                    "description": "主机IP列表",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "username": {
                    "description": "用户名",
                    "type": "string"
                }
            }
        },
        "http.UserPermissionGrantResponse": {
            "type": "object",
            "properties": {
                "failed_count": {
                    "description": "失败数量",
                    "type": "integer"
                },
                "failed_hosts": {
                    "description": "失败的主机及错误信息",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/http.HostPermissionError"
                    }
                },
                "permission_name": {
                    "description": "授权名称",
                    "type": "string"
                },
                "success_count": {
                    "description": "成功数量",
                    "type": "integer"
                },
                "success_hosts": {
                    "description": "成功授权的主机IP",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "total_hosts": {
                    "description": "总主机数",
                    "type": "integer"
                },
                "username": {
                    "description": "用户名",
                    "type": "string"
                }
            }
        },
        "http.UserPermissionRevokeRequest": {
            "type": "object",
            "required": [
                "username"
            ],
            "properties": {
                "host_ips": {
                    "description": "主机IP列表，可选。如果为空则删除整个权限规则",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "username": {
                    "description": "用户名",
                    "type": "string"
                }
            }
        },
        "http.UserPermissionRevokeResponse": {
            "type": "object",
            "properties": {
                "failed_count": {
                    "description": "失败数量",
                    "type": "integer"
                },
                "failed_hosts": {
                    "description": "失败的主机及错误信息",
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/http.HostPermissionError"
                    }
                },
                "success_count": {
                    "description": "成功数量",
                    "type": "integer"
                },
                "success_hosts": {
                    "description": "成功回收的主机IP",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "total_hosts": {
                    "description": "总主机数",
                    "type": "integer"
                },
                "username": {
                    "description": "用户名",
                    "type": "string"
                }
            }
        },
        "http.UserStatusResponse": {
            "type": "object",
            "properties": {
                "email": {
                    "type": "string"
                },
                "id": {
                    "type": "string"
                },
                "is_active": {
                    "type": "boolean"
                },
                "name": {
                    "type": "string"
                },
                "username": {
                    "type": "string"
                }
            }
        },
        "http.hostAddForm": {
            "type": "object",
            "required": [
                "ident",
                "ip"
            ],
            "properties": {
                "cate": {
                    "type": "string"
                },
                "cpu": {
                    "type": "string"
                },
                "cpu_model": {
                    "description": "CPU型号",
                    "type": "string"
                },
                "disk": {
                    "type": "string"
                },
                "fields": {
                    "description": "自定义字段",
                    "type": "object",
                    "additionalProperties": {
                        "type": "string"
                    }
                },
                "gpu": {
                    "type": "string"
                },
                "gpu_model": {
                    "description": "GPU型号",
                    "type": "string"
                },
                "idc": {
                    "type": "string"
                },
                "ident": {
                    "type": "string"
                },
                "ip": {
                    "type": "string"
                },
                "kernel_version": {
                    "description": "内核版本",
                    "type": "string"
                },
                "manufacturer": {
                    "type": "string"
                },
                "mem": {
                    "type": "string"
                },
                "model": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "note": {
                    "type": "string"
                },
                "os_version": {
                    "description": "操作系统版本",
                    "type": "string"
                },
                "rack": {
                    "description": "机架",
                    "type": "string"
                },
                "sn": {
                    "type": "string"
                },
                "tenant": {
                    "type": "string"
                },
                "zone": {
                    "description": "可用区",
                    "type": "string"
                }
            }
        },
        "http.hostCateForm": {
            "type": "object",
            "properties": {
                "cate": {
                    "type": "string"
                },
                "ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "http.hostFieldValueForm": {
            "type": "object",
            "required": [
                "field_idents",
                "host_ids"
            ],
            "properties": {
                "field_idents": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "host_ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                }
            }
        },
        "http.hostNoteForm": {
            "type": "object",
            "properties": {
                "ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "note": {
                    "type": "string"
                }
            }
        },
        "http.hostUpdateForm": {
            "type": "object",
            "properties": {
                "cate": {
                    "type": "string"
                },
                "cpu": {
                    "type": "string"
                },
                "cpu_model": {
                    "type": "string"
                },
                "disk": {
                    "type": "string"
                },
                "gpu": {
                    "type": "string"
                },
                "gpu_model": {
                    "type": "string"
                },
                "idc": {
                    "type": "string"
                },
                "kernel_version": {
                    "type": "string"
                },
                "manufacturer": {
                    "type": "string"
                },
                "mem": {
                    "type": "string"
                },
                "model": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "note": {
                    "type": "string"
                },
                "os_version": {
                    "type": "string"
                },
                "rack": {
                    "type": "string"
                },
                "sn": {
                    "type": "string"
                },
                "tenant": {
                    "type": "string"
                },
                "zone": {
                    "type": "string"
                }
            }
        },
        "http.idsOrIpsForm": {
            "type": "object",
            "properties": {
                "ids": {
                    "type": "array",
                    "items": {
                        "type": "integer"
                    }
                },
                "ips": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                }
            }
        },
        "models.Host": {
            "type": "object",
            "properties": {
                "cate": {
                    "type": "string"
                },
                "clock": {
                    "type": "integer"
                },
                "cpu": {
                    "type": "string"
                },
                "cpu_model": {
                    "description": "CPU型号",
                    "type": "string"
                },
                "current_group": {
                    "description": "当前所在用户组",
                    "type": "string"
                },
                "disk": {
                    "type": "string"
                },
                "gpu": {
                    "type": "string"
                },
                "gpu_model": {
                    "description": "GPU型号",
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                },
                "idc": {
                    "type": "string"
                },
                "ident": {
                    "type": "string"
                },
                "ip": {
                    "type": "string"
                },
                "jumpserver_asset_id": {
                    "description": "JumpServer资产ID",
                    "type": "string"
                },
                "jumpserver_node_id": {
                    "description": "JumpServer节点ID",
                    "type": "string"
                },
                "kernel_version": {
                    "description": "内核版本",
                    "type": "string"
                },
                "manufacturer": {
                    "type": "string"
                },
                "mem": {
                    "type": "string"
                },
                "model": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                },
                "note": {
                    "type": "string"
                },
                "os_version": {
                    "description": "操作系统版本",
                    "type": "string"
                },
                "rack": {
                    "description": "机架",
                    "type": "string"
                },
                "sn": {
                    "type": "string"
                },
                "tenant": {
                    "type": "string"
                },
                "zone": {
                    "description": "可用区",
                    "type": "string"
                }
            }
        },
        "models.HostField": {
            "type": "object",
            "properties": {
                "field_cate": {
                    "type": "string"
                },
                "field_extra": {
                    "type": "string"
                },
                "field_ident": {
                    "type": "string"
                },
                "field_name": {
                    "type": "string"
                },
                "field_required": {
                    "type": "integer"
                },
                "field_type": {
                    "type": "string"
                },
                "id": {
                    "type": "integer"
                }
            }
        },
        "models.HostFieldValue": {
            "type": "object",
            "properties": {
                "field_ident": {
                    "type": "string"
                },
                "field_value": {
                    "type": "string"
                },
                "host_id": {
                    "type": "integer"
                },
                "id": {
                    "type": "integer"
                }
            }
        }
    },
    "securityDefinitions": {
        "ApiKeyAuth": {
            "description": "API Token 认证",
            "type": "apiKey",
            "name": "X-User-Token",
            "in": "header"
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "",
	BasePath:         "/api/ams-ce",
	Schemes:          []string{},
	Title:            "AMS API",
	Description:      "主机资产管理系统 API 文档",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
