# 未同步主机查询API文档

## 概述

该API用于查询AMS数据库中尚未同步到JumpServer的主机列表。主要用于运维人员识别需要同步的主机，便于进行批量同步操作。

## 接口信息

- **URL**: `/api/ams-ce/hosts/unsynced`
- **方法**: `GET`
- **认证**: 需要（X-User-Token）
- **权限**: 需要用户登录权限

## 查询条件

该接口会自动过滤出满足以下条件的主机：
- `jump_server_asset_id` 字段为空字符串 (`''`)
- 或 `jump_server_asset_id` 字段为 `NULL`

## 查询参数

### 分页参数

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| limit | integer | 否 | 20 | 每页数量 |
| p | integer | 否 | 1 | 页码 |

### 过滤参数

所有过滤参数都支持批量查询，多个值用逗号分隔。

| 参数 | 类型 | 说明 | 示例 |
|------|------|------|------|
| id | string | 主机ID查询 | "1,2,3" |
| sn | string | 序列号查询 | "SN001,SN002" |
| ip | string | IP地址查询 | "***********,***********" |
| ident | string | 标识符查询 | "host-001,host-002" |
| name | string | 主机名查询 | "web-server,db-server" |
| os_version | string | 操作系统版本查询 | "Ubuntu 20.04,CentOS 7" |
| kernel_version | string | 内核版本查询 | "5.4.0,3.10.0" |
| cpu_model | string | CPU型号查询 | "Intel Xeon,AMD EPYC" |
| gpu_model | string | GPU型号查询 | "NVIDIA Tesla V100" |
| zone | string | 可用区查询 | "us-west-1a,us-west-1b" |
| rack | string | 机架查询 | "rack-01,rack-02" |
| note | string | 备注查询 | "production,testing" |
| model | string | 型号查询 | "Dell R740,HP DL380" |
| manufacturer | string | 厂商查询 | "Dell,HP,Lenovo" |
| idc | string | IDC机房查询 | "beijing,shanghai" |
| gpu | string | GPU卡数查询 | "1,2,4" |
| cpu | string | CPU核数查询 | "8,16,32" |
| mem | string | 内存查询 | "16GB,32GB,64GB" |
| disk | string | 磁盘查询 | "500GB,1TB,2TB" |
| cate | string | 类别查询 | "host,vm,container" |
| tenant | string | 租户查询 | "team-a,team-b" |

## 响应格式

### 成功响应

```json
{
  "code": 0,
  "dat": {
    "list": [
      {
        "id": 1,
        "sn": "SN001",
        "ip": "*************",
        "ident": "web-server-01",
        "name": "Web Server 01",
        "os_version": "Ubuntu 20.04",
        "kernel_version": "5.4.0-74-generic",
        "cpu_model": "Intel Xeon E5-2680 v4",
        "gpu_model": "NVIDIA Tesla V100",
        "zone": "us-west-1a",
        "rack": "rack-01",
        "note": "Production web server",
        "model": "Dell PowerEdge R740",
        "manufacturer": "Dell",
        "idc": "beijing",
        "gpu": "2",
        "cpu": "16",
        "mem": "64GB",
        "disk": "1TB",
        "cate": "host",
        "tenant": "team-a",
        "clock": 1640995200,
        "jump_server_asset_id": ""
      }
    ],
    "total": 1
  },
  "err": ""
}
```

### 错误响应

```json
{
  "code": 2,
  "dat": null,
  "err": "未授权"
}
```

## 使用示例

### 1. 基本查询

```bash
curl -H "X-User-Token: your-token" \
     "http://localhost:8080/api/ams-ce/hosts/unsynced"
```

### 2. 分页查询

```bash
curl -H "X-User-Token: your-token" \
     "http://localhost:8080/api/ams-ce/hosts/unsynced?limit=10&p=2"
```

### 3. 按类别过滤

```bash
curl -H "X-User-Token: your-token" \
     "http://localhost:8080/api/ams-ce/hosts/unsynced?cate=host"
```

### 4. 多条件过滤

```bash
curl -H "X-User-Token: your-token" \
     "http://localhost:8080/api/ams-ce/hosts/unsynced?cate=host&idc=beijing&limit=20"
```

### 5. 批量查询

```bash
curl -H "X-User-Token: your-token" \
     "http://localhost:8080/api/ams-ce/hosts/unsynced?cate=host,vm&manufacturer=Dell,HP"
```

## 状态码说明

| 状态码 | 说明 |
|--------|------|
| 0 | 成功 |
| 1 | 通用错误 |
| 2 | 未授权 |
| 3 | 资源不存在 |
| 4 | 参数验证错误 |
| 5 | 重复数据 |
| 6 | 服务器内部错误 |

## 使用场景

### 1. 运维巡检
定期查询未同步的主机，确保所有主机都已同步到JumpServer。

### 2. 批量同步准备
在执行批量同步操作前，先查询需要同步的主机列表。

### 3. 数据一致性检查
验证AMS和JumpServer之间的数据一致性。

### 4. 报表统计
生成未同步主机的统计报表。

## 性能建议

1. **合理使用分页**：对于大量数据，建议使用适当的分页大小（建议20-100）
2. **精确过滤**：使用具体的过滤条件减少返回的数据量
3. **避免频繁调用**：建议根据业务需要合理控制调用频率

## 测试工具

项目提供了测试脚本来验证API功能：

### Go测试脚本
```bash
go run src/modules/ams/scripts/test_unsynced_api.go [BASE_URL] [API_TOKEN]
```

### Shell测试脚本
```bash
./src/modules/ams/scripts/test_unsynced_api.sh [BASE_URL] [API_TOKEN]
```

## 注意事项

1. **权限要求**：需要有效的API Token
2. **数据实时性**：返回的是当前数据库中的实时数据
3. **字段完整性**：返回的主机信息包含所有基础字段和自定义字段
4. **排序规则**：结果按主机标识符（ident）排序

## 相关API

- `GET /api/ams-ce/hosts` - 获取所有主机列表
- `POST /api/ams-ce/hosts/sync` - 批量同步主机到JumpServer
- `GET /api/ams-ce/sync/status` - 查看同步管理器状态
