basePath: /api/ams-ce
definitions:
  http.ApiResponse:
    properties:
      code:
        type: integer
      dat: {}
      err:
        type: string
    type: object
  http.HostListResponse:
    properties:
      list:
        items: {}
        type: array
      total:
        type: integer
    type: object
  http.HostPermissionError:
    properties:
      error:
        description: 错误信息
        type: string
      host_ip:
        description: 主机IP
        type: string
    type: object
  http.HostSyncRequest:
    properties:
      host_ids:
        items:
          type: integer
        type: array
    required:
    - host_ids
    type: object
  http.ImportResult:
    properties:
      failed_count:
        type: integer
      import_id:
        type: string
      success_count:
        type: integer
      total_rows:
        type: integer
    type: object
  http.UserCreateRequest:
    properties:
      display_name:
        type: string
      email:
        type: string
      phone:
        type: string
      username:
        type: string
    required:
    - display_name
    - email
    - username
    type: object
  http.UserCreateResponse:
    properties:
      email:
        type: string
      id:
        type: string
      is_active:
        type: boolean
      name:
        type: string
      password:
        description: 添加密码字段
        type: string
      username:
        type: string
    type: object
  http.UserHostsResponse:
    properties:
      hosts:
        items:
          $ref: '#/definitions/models.Host'
        type: array
      total:
        type: integer
      username:
        type: string
    type: object
  http.UserPermissionGrantRequest:
    properties:
      host_ips:
        description: 主机IP列表
        items:
          type: string
        type: array
      username:
        description: 用户名
        type: string
    required:
    - host_ips
    - username
    type: object
  http.UserPermissionGrantResponse:
    properties:
      failed_count:
        description: 失败数量
        type: integer
      failed_hosts:
        description: 失败的主机及错误信息
        items:
          $ref: '#/definitions/http.HostPermissionError'
        type: array
      permission_name:
        description: 授权名称
        type: string
      success_count:
        description: 成功数量
        type: integer
      success_hosts:
        description: 成功授权的主机IP
        items:
          type: string
        type: array
      total_hosts:
        description: 总主机数
        type: integer
      username:
        description: 用户名
        type: string
    type: object
  http.UserPermissionRevokeRequest:
    properties:
      host_ips:
        description: 主机IP列表，可选。如果为空则删除整个权限规则
        items:
          type: string
        type: array
      username:
        description: 用户名
        type: string
    required:
    - username
    type: object
  http.UserPermissionRevokeResponse:
    properties:
      failed_count:
        description: 失败数量
        type: integer
      failed_hosts:
        description: 失败的主机及错误信息
        items:
          $ref: '#/definitions/http.HostPermissionError'
        type: array
      success_count:
        description: 成功数量
        type: integer
      success_hosts:
        description: 成功回收的主机IP
        items:
          type: string
        type: array
      total_hosts:
        description: 总主机数
        type: integer
      username:
        description: 用户名
        type: string
    type: object
  http.UserStatusResponse:
    properties:
      email:
        type: string
      id:
        type: string
      is_active:
        type: boolean
      name:
        type: string
      username:
        type: string
    type: object
  http.hostAddForm:
    properties:
      cate:
        type: string
      cpu:
        type: string
      cpu_model:
        description: CPU型号
        type: string
      disk:
        type: string
      fields:
        additionalProperties:
          type: string
        description: 自定义字段
        type: object
      gpu:
        type: string
      gpu_model:
        description: GPU型号
        type: string
      idc:
        type: string
      ident:
        type: string
      ip:
        type: string
      kernel_version:
        description: 内核版本
        type: string
      manufacturer:
        type: string
      mem:
        type: string
      model:
        type: string
      name:
        type: string
      note:
        type: string
      os_version:
        description: 操作系统版本
        type: string
      rack:
        description: 机架
        type: string
      sn:
        type: string
      tenant:
        type: string
      zone:
        description: 可用区
        type: string
    required:
    - ident
    - ip
    type: object
  http.hostCateForm:
    properties:
      cate:
        type: string
      ids:
        items:
          type: integer
        type: array
    type: object
  http.hostFieldValueForm:
    properties:
      field_idents:
        items:
          type: string
        type: array
      host_ids:
        items:
          type: integer
        type: array
    required:
    - field_idents
    - host_ids
    type: object
  http.hostNoteForm:
    properties:
      ids:
        items:
          type: integer
        type: array
      note:
        type: string
    type: object
  http.hostUpdateForm:
    properties:
      cate:
        type: string
      cpu:
        type: string
      cpu_model:
        type: string
      disk:
        type: string
      gpu:
        type: string
      gpu_model:
        type: string
      idc:
        type: string
      kernel_version:
        type: string
      manufacturer:
        type: string
      mem:
        type: string
      model:
        type: string
      name:
        type: string
      note:
        type: string
      os_version:
        type: string
      rack:
        type: string
      sn:
        type: string
      tenant:
        type: string
      zone:
        type: string
    type: object
  http.idsOrIpsForm:
    properties:
      ids:
        items:
          type: integer
        type: array
      ips:
        items:
          type: string
        type: array
    type: object
  models.Host:
    properties:
      cate:
        type: string
      clock:
        type: integer
      cpu:
        type: string
      cpu_model:
        description: CPU型号
        type: string
      current_group:
        description: 当前所在用户组
        type: string
      disk:
        type: string
      gpu:
        type: string
      gpu_model:
        description: GPU型号
        type: string
      id:
        type: integer
      idc:
        type: string
      ident:
        type: string
      ip:
        type: string
      jumpserver_asset_id:
        description: JumpServer资产ID
        type: string
      jumpserver_node_id:
        description: JumpServer节点ID
        type: string
      kernel_version:
        description: 内核版本
        type: string
      manufacturer:
        type: string
      mem:
        type: string
      model:
        type: string
      name:
        type: string
      note:
        type: string
      os_version:
        description: 操作系统版本
        type: string
      rack:
        description: 机架
        type: string
      sn:
        type: string
      tenant:
        type: string
      zone:
        description: 可用区
        type: string
    type: object
  models.HostField:
    properties:
      field_cate:
        type: string
      field_extra:
        type: string
      field_ident:
        type: string
      field_name:
        type: string
      field_required:
        type: integer
      field_type:
        type: string
      id:
        type: integer
    type: object
  models.HostFieldValue:
    properties:
      field_ident:
        type: string
      field_value:
        type: string
      host_id:
        type: integer
      id:
        type: integer
    type: object
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: 主机资产管理系统 API 文档
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: AMS API
  version: "1.0"
paths:
  /host:
    post:
      consumes:
      - application/json
      description: 添加单台主机，支持基础字段和自定义字段。注意：IP、标识符(ident)和主机名(name)必须唯一，与JumpServer保持一致
      parameters:
      - description: 主机信息
        in: body
        name: host
        required: true
        schema:
          $ref: '#/definitions/http.hostAddForm'
      produces:
      - application/json
      responses:
        "200":
          description: 添加成功
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/models.Host'
              type: object
        "400":
          description: 请求参数错误（包括IP、ident或name重复）
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      summary: 添加主机
      tags:
      - 主机管理
  /host/{id}:
    get:
      consumes:
      - application/json
      description: 根据主机ID获取主机详细信息
      parameters:
      - description: 主机ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 主机信息
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/models.Host'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 主机不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      summary: 获取单个主机信息
      tags:
      - 主机管理
    put:
      consumes:
      - application/json
      description: 更新单台主机的详细信息，支持基础字段。IP、ID和标识符不可修改，主机名(name)必须唯一
      parameters:
      - description: 主机ID
        in: path
        name: id
        required: true
        type: integer
      - description: 主机更新参数
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/http.hostUpdateForm'
      produces:
      - application/json
      responses:
        "200":
          description: 更新后的主机信息
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/models.Host'
              type: object
        "400":
          description: 参数错误（包括name重复）
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 主机不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      summary: 更新主机信息
      tags:
      - 主机管理
  /host/{id}/fields:
    get:
      consumes:
      - application/json
      description: 获取指定主机的所有自定义字段值
      parameters:
      - description: 主机ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 字段值列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  items:
                    $ref: '#/definitions/models.HostFieldValue'
                  type: array
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      summary: 获取主机字段值
      tags:
      - 主机字段管理
    put:
      consumes:
      - application/json
      description: 批量更新指定主机的自定义字段值
      parameters:
      - description: 主机ID
        in: path
        name: id
        required: true
        type: integer
      - description: 字段值列表
        in: body
        name: fields
        required: true
        schema:
          items:
            $ref: '#/definitions/models.HostFieldValue'
          type: array
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      summary: 批量更新主机字段值
      tags:
      - 主机字段管理
  /hosts:
    delete:
      consumes:
      - application/json
      description: 批量删除主机设备，支持通过ID或IP地址删除
      parameters:
      - description: 主机ID或IP列表
        in: body
        name: hosts
        required: true
        schema:
          $ref: '#/definitions/http.idsOrIpsForm'
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      summary: 批量删除主机
      tags:
      - 主机管理
    get:
      consumes:
      - application/json
      description: 获取主机列表，支持分页和多字段查询。支持单个查询和批量查询（多个值用逗号分隔）
      parameters:
      - default: 20
        description: 每页数量
        in: query
        name: limit
        type: integer
      - default: 1
        description: 页码
        in: query
        name: p
        type: integer
      - description: 主机ID查询，支持批量查询，多个值用逗号分隔
        example: '"1,2,3"'
        in: query
        name: id
        type: string
      - description: 序列号查询，支持批量查询，多个值用逗号分隔
        example: '"SN001,SN002"'
        in: query
        name: sn
        type: string
      - description: IP地址查询，支持批量查询，多个值用逗号分隔
        example: '"***********,***********"'
        in: query
        name: ip
        type: string
      - description: 标识符查询，支持批量查询，多个值用逗号分隔
        example: '"host-001,host-002"'
        in: query
        name: ident
        type: string
      - description: 主机名查询，支持批量查询，多个值用逗号分隔
        example: '"web-server,db-server"'
        in: query
        name: name
        type: string
      - description: 操作系统版本查询，支持批量查询，多个值用逗号分隔
        example: '"Ubuntu 20.04,CentOS 7"'
        in: query
        name: os_version
        type: string
      - description: 内核版本查询，支持批量查询，多个值用逗号分隔
        example: '"5.4.0,3.10.0"'
        in: query
        name: kernel_version
        type: string
      - description: CPU型号查询，支持批量查询，多个值用逗号分隔
        example: '"Intel Xeon,AMD EPYC"'
        in: query
        name: cpu_model
        type: string
      - description: GPU型号查询，支持批量查询，多个值用逗号分隔
        example: '"NVIDIA Tesla V100,NVIDIA RTX 3080"'
        in: query
        name: gpu_model
        type: string
      - description: 可用区查询，支持批量查询，多个值用逗号分隔
        example: '"us-west-1a,us-west-1b"'
        in: query
        name: zone
        type: string
      - description: 机架查询，支持批量查询，多个值用逗号分隔
        example: '"rack-01,rack-02"'
        in: query
        name: rack
        type: string
      - description: 备注查询，支持批量查询，多个值用逗号分隔
        example: '"production,testing"'
        in: query
        name: note
        type: string
      - description: 型号查询，支持批量查询，多个值用逗号分隔
        example: '"Dell R740,HP DL380"'
        in: query
        name: model
        type: string
      - description: 厂商查询，支持批量查询，多个值用逗号分隔
        example: '"Dell,HP,Lenovo"'
        in: query
        name: manufacturer
        type: string
      - description: IDC机房查询，支持批量查询，多个值用逗号分隔
        example: '"beijing,shanghai"'
        in: query
        name: idc
        type: string
      - description: GPU卡数查询，支持批量查询，多个值用逗号分隔
        example: '"1,2,4"'
        in: query
        name: gpu
        type: string
      - description: CPU核数查询，支持批量查询，多个值用逗号分隔
        example: '"8,16,32"'
        in: query
        name: cpu
        type: string
      - description: 内存查询，支持批量查询，多个值用逗号分隔
        example: '"16GB,32GB,64GB"'
        in: query
        name: mem
        type: string
      - description: 磁盘查询，支持批量查询，多个值用逗号分隔
        example: '"500GB,1TB,2TB"'
        in: query
        name: disk
        type: string
      - description: 类别查询，支持批量查询，多个值用逗号分隔
        example: '"host,vm,container"'
        in: query
        name: cate
        type: string
      - description: 租户查询，支持批量查询，多个值用逗号分隔
        example: '"team-a,team-b"'
        in: query
        name: tenant
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 主机列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/http.HostListResponse'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      summary: 获取主机列表
      tags:
      - 主机管理
  /hosts/cate:
    put:
      consumes:
      - application/json
      description: 批量修改主机设备的类别信息
      parameters:
      - description: 类别信息
        in: body
        name: cate
        required: true
        schema:
          $ref: '#/definitions/http.hostCateForm'
      produces:
      - application/json
      responses:
        "200":
          description: 修改成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      summary: 批量修改主机类别
      tags:
      - 主机管理
  /hosts/csv/failed/{import_id}:
    get:
      consumes:
      - application/json
      description: 根据导入批次ID下载导入失败的CSV数据，包含原始数据和错误信息
      parameters:
      - description: 导入批次ID
        in: path
        name: import_id
        required: true
        type: string
      produces:
      - application/octet-stream
      responses:
        "200":
          description: 失败数据CSV文件
          schema:
            type: file
        "400":
          description: 参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 导入批次不存在或已过期
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      summary: 下载导入失败的数据
      tags:
      - 主机管理
  /hosts/csv/import:
    post:
      consumes:
      - multipart/form-data
      description: 通过CSV或Excel文件批量导入主机数据，支持基础字段和自定义字段。支持.csv、.xlsx格式。最大支持1000行数据导入，文件大小限制10MB。必填字段：IP地址、标识符、主机名、类别。IP地址格式校验：必须是有效的IPv4或IPv6地址，不允许0.0.0.0和***************。注意：不支持包含大量附件（图片、嵌入文档等）的Excel文件，请移除附件后重试。返回导入结果统计信息，详细错误信息可通过失败数据下载接口获取。成功导入的主机将自动异步同步到JumpServer，同步过程不影响导入操作的响应
      parameters:
      - description: CSV或Excel文件（支持.csv、.xlsx格式，最大1000行数据，文件大小限制10MB，不支持包含附件的文件）。必填字段：IP地址、标识符、主机名、类别。IP地址必须是有效格式，如*************
        in: formData
        name: file
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: 导入完成，返回统计信息。如果有失败记录，err字段会包含错误提示，import_id用于下载失败数据
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/http.ImportResult'
              type: object
        "400":
          description: 请求参数错误（如文件格式错误、文件过大、包含附件、必填字段缺失、IP地址格式无效、超过1000行限制等）
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "408":
          description: 请求超时（文件可能包含大量附件）
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      summary: 批量导入主机
      tags:
      - 主机管理
  /hosts/csv/template:
    get:
      consumes:
      - application/json
      description: 动态根据自定义字段生成主机导入模板文件，支持CSV和Excel格式
      parameters:
      - default: csv
        description: 文件格式
        enum:
        - csv
        - xlsx
        in: query
        name: format
        type: string
      produces:
      - application/octet-stream
      responses:
        "200":
          description: 模板文件
          schema:
            type: file
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      summary: 下载主机导入模板
      tags:
      - 主机管理
  /hosts/field/{id}:
    delete:
      consumes:
      - application/json
      description: 删除主机自定义字段
      parameters:
      - description: 字段ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 字段不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      summary: 删除主机字段
      tags:
      - 主机字段管理
    get:
      consumes:
      - application/json
      description: 根据字段ID获取主机字段详细信息
      parameters:
      - description: 字段ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 字段信息
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/models.HostField'
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 字段不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      summary: 获取单个主机字段
      tags:
      - 主机字段管理
    put:
      consumes:
      - application/json
      description: 更新主机自定义字段信息
      parameters:
      - description: 字段ID
        in: path
        name: id
        required: true
        type: integer
      - description: 字段信息
        in: body
        name: field
        required: true
        schema:
          $ref: '#/definitions/models.HostField'
      produces:
      - application/json
      responses:
        "200":
          description: 更新成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      summary: 更新主机字段
      tags:
      - 主机字段管理
  /hosts/fields:
    get:
      consumes:
      - application/json
      description: 获取所有主机自定义字段列表
      produces:
      - application/json
      responses:
        "200":
          description: 字段列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  items:
                    $ref: '#/definitions/models.HostField'
                  type: array
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      summary: 获取主机字段列表
      tags:
      - 主机字段管理
    post:
      consumes:
      - application/json
      description: 创建新的主机自定义字段
      parameters:
      - description: 字段信息
        in: body
        name: field
        required: true
        schema:
          $ref: '#/definitions/models.HostField'
      produces:
      - application/json
      responses:
        "200":
          description: 创建成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      summary: 创建主机字段
      tags:
      - 主机字段管理
  /hosts/fields/values:
    post:
      consumes:
      - application/json
      description: 获取多个主机的指定自定义字段值
      parameters:
      - description: 查询条件
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/http.hostFieldValueForm'
      produces:
      - application/json
      responses:
        "200":
          description: 字段值数据
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  additionalProperties: true
                  type: object
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      summary: 批量获取主机字段值
      tags:
      - 主机字段管理
  /hosts/note:
    put:
      consumes:
      - application/json
      description: 批量修改主机设备的备注信息
      parameters:
      - description: 备注信息
        in: body
        name: note
        required: true
        schema:
          $ref: '#/definitions/http.hostNoteForm'
      produces:
      - application/json
      responses:
        "200":
          description: 修改成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      summary: 批量修改主机备注
      tags:
      - 主机管理
  /hosts/sync:
    post:
      consumes:
      - application/json
      description: 将AMS中的主机同步到JumpServer的jump-ams节点下
      parameters:
      - description: 同步主机信息
        in: body
        name: sync
        required: true
        schema:
          $ref: '#/definitions/http.HostSyncRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 同步成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      summary: 同步主机到JumpServer
      tags:
      - 主机管理
  /permissions/grant-user:
    post:
      consumes:
      - application/json
      description: 批量给用户更新机器授权(有则更新，无则新建)，使用用户名和IP地址作为参数。授权名称格式为"perf-用户名"。自动授权全部协议和全部账号
      parameters:
      - description: 用户权限授权信息
        in: body
        name: permission
        required: true
        schema:
          $ref: '#/definitions/http.UserPermissionGrantRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 授权结果
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/http.UserPermissionGrantResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      summary: 批量给用户更新机器授权
      tags:
      - 权限管理
  /permissions/revoke-user:
    post:
      consumes:
      - application/json
      description: 回收指定用户的访问权限。如果提供host_ips则只回收指定机器的权限，如果不提供host_ips则删除整个权限规则
      parameters:
      - description: 用户权限回收信息
        in: body
        name: permission
        required: true
        schema:
          $ref: '#/definitions/http.UserPermissionRevokeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 回收结果
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/http.UserPermissionRevokeResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      summary: 回收用户权限
      tags:
      - 权限管理
  /ping:
    get:
      consumes:
      - application/json
      description: 检查服务器运行状态
      produces:
      - application/json
      responses:
        "200":
          description: pong
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  type: string
              type: object
      summary: 健康检查
      tags:
      - 系统
  /sync/reset:
    post:
      consumes:
      - application/json
      description: 重置全局同步管理器，重新建立JumpServer连接。用于配置更新后的重连
      produces:
      - application/json
      responses:
        "200":
          description: 重置成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      summary: 重置同步管理器
      tags:
      - 系统管理
  /sync/status:
    get:
      consumes:
      - application/json
      description: 获取全局同步管理器的状态和统计信息，用于监控和调试
      produces:
      - application/json
      responses:
        "200":
          description: 同步管理器状态信息
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  additionalProperties: true
                  type: object
              type: object
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      summary: 获取同步管理器状态
      tags:
      - 系统管理
  /users:
    post:
      consumes:
      - application/json
      description: 创建用户并同步到JumpServer，自动生成随机密码
      parameters:
      - description: 用户信息
        in: body
        name: user
        required: true
        schema:
          $ref: '#/definitions/http.UserCreateRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 创建成功，返回用户信息和生成的密码
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/http.UserCreateResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      summary: 创建用户
      tags:
      - 用户管理
  /users/{username}:
    delete:
      consumes:
      - application/json
      description: 删除用户（从JumpServer中删除）
      parameters:
      - description: 用户名
        in: path
        name: username
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 删除成功
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 用户不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      summary: 删除用户
      tags:
      - 用户管理
  /users/{username}/disable:
    put:
      consumes:
      - application/json
      description: 禁用用户（在JumpServer中设置为非激活状态）
      parameters:
      - description: 用户名
        in: path
        name: username
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 禁用成功
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/http.UserStatusResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 用户不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      summary: 禁用用户
      tags:
      - 用户管理
  /users/{username}/enable:
    put:
      consumes:
      - application/json
      description: 激活用户（在JumpServer中设置为激活状态）
      parameters:
      - description: 用户名
        in: path
        name: username
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 激活成功
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/http.UserStatusResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 用户不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      summary: 激活用户
      tags:
      - 用户管理
  /users/{username}/hosts:
    get:
      consumes:
      - application/json
      description: 查询用户在JumpServer中有权限访问的主机列表
      parameters:
      - description: 用户名
        in: path
        name: username
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 用户主机权限列表
          schema:
            allOf:
            - $ref: '#/definitions/http.ApiResponse'
            - properties:
                dat:
                  $ref: '#/definitions/http.UserHostsResponse'
              type: object
        "400":
          description: 请求参数错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "401":
          description: 未授权
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "404":
          description: 用户不存在
          schema:
            $ref: '#/definitions/http.ApiResponse'
        "500":
          description: 服务器错误
          schema:
            $ref: '#/definitions/http.ApiResponse'
      security:
      - ApiKeyAuth: []
      summary: 查询用户权限的主机列表
      tags:
      - 用户管理
securityDefinitions:
  ApiKeyAuth:
    description: API Token 认证
    in: header
    name: X-User-Token
    type: apiKey
swagger: "2.0"
