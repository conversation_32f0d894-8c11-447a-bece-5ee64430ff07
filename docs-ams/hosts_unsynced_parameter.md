# 主机查询API - unsynced参数说明

## 概述

在现有的 `/api/ams-ce/hosts` 接口中新增了 `unsynced` 查询参数，用于筛选尚未同步到JumpServer的主机。

## 使用方法

### 基本语法

```
GET /api/ams-ce/hosts?unsynced=true
```

### 参数说明

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| unsynced | string | 否 | 设置为 "true" 时只返回未同步到JumpServer的主机 |

### 筛选条件

当 `unsynced=true` 时，系统会自动过滤出满足以下条件的主机：
- `jump_server_asset_id` 字段为空字符串 (`''`)
- 或 `jump_server_asset_id` 字段为 `NULL`

## 使用示例

### 1. 获取所有未同步主机

```bash
curl -H "X-User-Token: your-token" \
     "http://localhost:8080/api/ams-ce/hosts?unsynced=true"
```

### 2. 结合其他查询条件

```bash
# 获取未同步的host类型主机
curl -H "X-User-Token: your-token" \
     "http://localhost:8080/api/ams-ce/hosts?unsynced=true&cate=host"

# 获取beijing机房未同步的主机
curl -H "X-User-Token: your-token" \
     "http://localhost:8080/api/ams-ce/hosts?unsynced=true&idc=beijing"

# 多条件组合查询
curl -H "X-User-Token: your-token" \
     "http://localhost:8080/api/ams-ce/hosts?unsynced=true&cate=host&idc=beijing&limit=20"
```

### 3. 分页查询

```bash
curl -H "X-User-Token: your-token" \
     "http://localhost:8080/api/ams-ce/hosts?unsynced=true&limit=10&p=2"
```

## 响应格式

响应格式与原有的 `/hosts` 接口完全一致：

```json
{
  "code": 0,
  "dat": {
    "list": [
      {
        "id": 1,
        "ip": "*************",
        "ident": "web-server-01",
        "name": "Web Server 01",
        "jump_server_asset_id": "",
        // ... 其他字段
      }
    ],
    "total": 1
  },
  "err": ""
}
```

## 兼容性

- ✅ 完全向后兼容：不传 `unsynced` 参数时行为与原来完全一致
- ✅ 支持所有现有的查询参数和过滤条件
- ✅ 支持分页、排序等功能
- ✅ 响应格式保持不变

## 使用场景

1. **运维巡检**：定期检查哪些主机还没有同步到JumpServer
2. **批量同步准备**：在执行批量同步前先查看需要同步的主机
3. **数据一致性检查**：验证AMS和JumpServer之间的数据一致性
4. **报表统计**：生成未同步主机的统计报表

## 测试工具

项目提供了测试脚本来验证功能：

```bash
# Go测试脚本
go run src/modules/ams/scripts/test_unsynced_api.go [BASE_URL] [API_TOKEN]

# Shell测试脚本  
./src/modules/ams/scripts/test_unsynced_api.sh [BASE_URL] [API_TOKEN]
```

## 性能说明

- 使用了专门的数据库查询优化，性能与普通查询相当
- 支持所有现有的索引和查询优化
- 建议合理使用分页以获得最佳性能

## 注意事项

1. `unsynced` 参数只接受 "true" 值，其他值（包括 "false"）都会被忽略
2. 该参数可以与所有现有的查询参数组合使用
3. 查询结果按主机标识符（ident）排序
