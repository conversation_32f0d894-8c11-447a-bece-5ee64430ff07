# AMS JumpServer 集成文档

本目录包含 AMS 与 JumpServer 集成相关的技术文档。

## 📚 文档目录

### 核心文档

- **[JumpServer 集成功能说明](../AMS_JUMPSERVER_INTEGRATION.md)** - 完整的功能介绍和使用指南
- **[自动同步功能说明](../AMS_AUTO_SYNC_SUMMARY.md)** - 机器自动同步功能详解

### 修复记录

- **[修复总览](fixes-summary.md)** - 所有修复的汇总和统计
- **[密码创建问题修复记录](jumpserver-password-fix.md)** - 用户密码创建问题的详细修复过程
- **[JumpServer 同步修复记录](jumpserver-sync-fix.md)** - 主机同步到 JumpServer 的问题修复
- **[Control 脚本日志修复记录](control-script-logging-fix.md)** - 日志系统重定向问题修复

### 使用指南

- **[故障排查指南](jumpserver-troubleshooting.md)** - 常见问题和解决方案
- **[日志使用指南](logging-guide.md)** - 日志系统使用和问题排查

## 🚀 快速开始

### 1. 基本配置

```yaml
# etc/ams.yml
jumpserver:
  base_url: "http://your-jumpserver.com:8090"
  username: "admin"
  password: "your-password"
  token: "your-api-token"  # 推荐使用 token
  organization: "Default"
  timeout: 30
```

### 2. 功能验证

```bash
# 测试连接
curl -H "Authorization: Token your-token" \
  http://your-jumpserver.com:8090/api/v1/users/profile/

# 创建用户
curl -X POST http://localhost:8080/api/ams-ce/users \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "display_name": "测试用户",
    "email": "<EMAIL>"
  }'
```

## 🔧 主要功能

### 用户管理
- ✅ 创建用户（自动生成安全密码）
- ✅ 删除用户
- ✅ 查询用户权限

### 权限管理
- ✅ 用户组权限授权
- ✅ 权限回收
- ✅ 批量权限操作

### 机器管理
- ✅ 主机自动同步
- ✅ 节点动态创建
- ✅ 批量机器导入

## 🐛 问题解决

### 常见问题

1. **用户创建后无法登录**
   - 参考：[密码创建问题修复记录](jumpserver-password-fix.md)
   - 解决：已通过两步创建流程修复

2. **API 连接失败**
   - 检查 JumpServer 服务状态
   - 验证网络连通性和配置

3. **权限不足**
   - 确认 API Token 权限
   - 检查用户角色配置

详细排查步骤请参考：[故障排查指南](jumpserver-troubleshooting.md)

## 📊 技术架构

```
AMS API Layer
     ↓
JumpServer Service Layer
     ↓
JumpServer Client
     ↓
JumpServer REST API
```

### 关键组件

- **Client Layer** (`jumpserver/client.go`) - HTTP 客户端封装
- **Service Layer** (`service/jumpserver.go`) - 业务逻辑封装
- **HTTP Layer** (`http/router_user.go`) - REST API 接口
- **Types** (`jumpserver/types.go`) - 数据结构定义

## 🔒 安全考虑

### 认证方式
- 推荐使用 API Token 而非用户名密码
- 定期轮换 Token
- 使用 HTTPS 连接

### 密码安全
- 自动生成 12 位强密码
- 包含大小写字母、数字、特殊字符
- 使用加密安全的随机数生成器

### 网络安全
- 配置适当的防火墙规则
- 使用内网连接
- 启用访问日志记录

## 📈 性能优化

### 批量操作
- 使用并发处理提高效率
- 控制并发数避免过载
- 实现重试机制

### 缓存策略
- 缓存用户组信息
- 缓存节点结构
- 定期刷新缓存

### 监控指标
- API 响应时间
- 成功率统计
- 错误率监控
- 资源使用情况

## 🧪 测试

### 单元测试
```bash
go test ./src/modules/ams/jumpserver/...
```

### 集成测试
```bash
go test ./src/modules/ams/service/...
```

### 手动测试
参考各文档中的测试用例和验证步骤。

## 📝 开发指南

### 添加新功能
1. 在 `types.go` 中定义数据结构
2. 在 `client.go` 中实现 API 调用
3. 在 `service.go` 中封装业务逻辑
4. 在 `router_*.go` 中添加 HTTP 接口
5. 更新文档和测试

### 代码规范
- 遵循 Go 语言规范
- 添加适当的错误处理
- 编写清晰的注释
- 包含单元测试

## 📞 支持

### 文档更新
如发现文档问题或需要补充，请：
1. 创建 Issue 描述问题
2. 提交 Pull Request 修复
3. 联系维护团队

### 技术支持
- 查看故障排查指南
- 检查相关日志
- 联系开发团队

---

**文档版本**：v1.0  
**最后更新**：2025-07-31  
**维护团队**：AMS 开发组
