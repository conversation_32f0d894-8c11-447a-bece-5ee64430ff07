# JumpServer 集成故障排查指南

## 🚨 常见问题

### 1. 用户创建后无法登录

**症状**：
- API 创建用户成功（返回 201）
- 使用生成的密码登录失败
- 错误信息：`password_failed`

**原因**：
JumpServer POST 创建用户时直接设置密码存在兼容性问题

**解决方案**：
使用两步创建流程（已在代码中实现）

**详细修复记录**：参考 [密码创建问题修复记录](jumpserver-password-fix.md)

**验证方法**：
```bash
# 1. 创建用户
curl -X POST http://jumpserver:8090/api/ams-ce/users \
  -H "Content-Type: application/json" \
  -d '{"username":"test","display_name":"测试","email":"<EMAIL>"}'

# 2. 使用返回的密码登录测试
curl -X POST http://jumpserver:8090/api/v1/authentication/auth/ \
  -H "Content-Type: application/json" \
  -d '{"username":"test","password":"返回的密码"}'
```

### 2. API 连接失败

**症状**：
- `connection failed` 错误
- 超时错误

**排查步骤**：
1. 检查 JumpServer 服务状态
2. 验证网络连通性
3. 确认配置文件中的 URL 正确

**验证命令**：
```bash
# 检查服务状态
curl -I http://jumpserver-host:8090/api/v1/users/profile/

# 检查认证
curl -H "Authorization: Token your-token" \
  http://jumpserver-host:8090/api/v1/users/profile/
```

### 3. 认证失败

**症状**：
- `authentication failed: status=401`
- `Authentication credentials were not provided`

**解决方案**：
1. 检查 Token 是否正确
2. 确认 Organization 设置
3. 验证 Token 是否过期

**配置检查**：
```yaml
jumpserver:
  base_url: "http://jumpserver-host:8090"
  token: "your-valid-token"
  organization: "Default"
```

### 4. 主机同步到 JumpServer 失败

**症状**：
- 创建主机成功，但 JumpServer 中没有对应资产
- 日志中出现：`"Cannot create asset directly, you should create a host or other"`
- 同步失败错误

**原因**：
JumpServer API 版本变化，不再支持直接创建 asset

**解决方案**：
已修复为使用 hosts 端点创建主机（已在代码中实现）

**详细修复记录**：参考 [JumpServer 同步修复记录](jumpserver-sync-fix.md)

**验证方法**：
```bash
# 创建主机并检查同步日志
curl -X POST http://localhost:8002/api/ams-ce/host \
  -H "X-User-Token: your-token" \
  -d '{"ip":"*************","ident":"test","name":"测试主机"}'

# 检查同步日志
grep "sync" logs/ams/INFO.log
```

### 5. 日志文件为空或日志混乱

**症状**：
- `logs/ams/INFO.log`、`logs/ams/ERROR.log` 等文件为空
- 所有日志都在 `logs/ams/stdout.log` 中
- 无法按等级查看日志

**原因**：
control 脚本的重定向覆盖了应用程序的日志配置

**解决方案**：
已修复 control 脚本的重定向逻辑（已在代码中实现）

**详细修复记录**：参考 [Control 脚本日志修复记录](control-script-logging-fix.md)

**验证方法**：
```bash
# 重启服务
bash control restart ams

# 检查日志文件
ls -la logs/ams/
tail -5 logs/ams/INFO.log
```

### 6. 权限不足

**症状**：
- `permission_denied` 错误
- 某些操作无法执行

**解决方案**：
确保 API Token 对应的用户具有足够权限：
- 用户管理权限
- 资产管理权限
- 权限管理权限

## 🔧 调试工具

### 1. 连接测试脚本

```go
package main

import (
    "fmt"
    "time"
    "arboris/src/modules/ams/jumpserver"
)

func main() {
    client := jumpserver.NewClient(
        "http://your-jumpserver:8090",
        "", "", // 用户名密码留空
        "your-token",
        "Default",
        30*time.Second,
    )
    
    if err := client.TestConnection(); err != nil {
        fmt.Printf("连接失败: %v\n", err)
    } else {
        fmt.Println("连接成功!")
    }
}
```

### 2. 用户创建测试

```bash
#!/bin/bash

BASE_URL="http://jumpserver-host:8090"
TOKEN="your-token"

# 测试创建用户
echo "测试创建用户..."
response=$(curl -s -X POST ${BASE_URL}/api/ams-ce/users \
  -H "Content-Type: application/json" \
  -H "Authorization: Token ${TOKEN}" \
  -d '{
    "username": "debuguser",
    "display_name": "调试用户",
    "email": "<EMAIL>",
    "phone": "13800138000"
  }')

echo "响应: $response"

# 提取密码并测试登录
password=$(echo "$response" | jq -r '.dat.password')
if [ "$password" != "null" ] && [ -n "$password" ]; then
    echo "测试登录..."
    login_response=$(curl -s -X POST ${BASE_URL}/api/v1/authentication/auth/ \
      -H "Content-Type: application/json" \
      -d "{\"username\":\"debuguser\",\"password\":\"${password}\"}")
    
    echo "登录响应: $login_response"
else
    echo "未获取到密码"
fi
```

## 📊 监控检查点

### 1. 健康检查

定期检查以下指标：
- JumpServer API 响应时间
- 用户创建成功率
- 登录验证成功率
- 错误率统计

### 2. 日志监控

关键日志位置：
- **AMS 应用日志**：`logs/ams/` 目录，按等级分文件
  - `INFO.log` - 一般信息日志
  - `ERROR.log` - 错误日志
  - `WARNING.log` - 警告日志
  - `DEBUG.log` - 调试日志
  - `ALL.log` - 所有等级日志
- **启动日志**：`logs/ams/startup.log` - 服务启动信息
- **JumpServer 日志**：查看 JumpServer 服务日志

重要日志关键词：
- `JumpServer connection test successful` - 连接成功
- `failed to create user` - 用户创建失败
- `failed to set password` - 密码设置失败
- `API error` - API 调用错误
- `Host sync failed` - 主机同步失败

### 3. 性能监控

监控指标：
- API 调用延迟
- 并发用户创建性能
- 内存使用情况
- 网络连接数

## 🔍 常用调试命令

### 检查 JumpServer 状态
```bash
# 检查服务状态
systemctl status jumpserver

# 检查端口监听
netstat -tlnp | grep 8090

# 检查进程
ps aux | grep jumpserver
```

### 检查 AMS 配置
```bash
# 查看配置文件
cat etc/ams.yml

# 检查不同等级的日志
tail -f logs/ams/INFO.log      # 一般信息
tail -f logs/ams/ERROR.log     # 错误信息
tail -f logs/ams/WARNING.log   # 警告信息
tail -f logs/ams/ALL.log       # 所有日志

# 查看启动日志
cat logs/ams/startup.log

# 实时监控多个日志文件
tail -f logs/ams/INFO.log logs/ams/ERROR.log logs/ams/WARNING.log
```

### 网络连通性测试
```bash
# 基本连通性
ping jumpserver-host

# 端口连通性
telnet jumpserver-host 8090

# HTTP 连通性
curl -I http://jumpserver-host:8090
```

## 📞 获取帮助

### 1. 日志收集

遇到问题时，请收集以下信息：
- AMS 配置文件 (`etc/ams.yml`)
- 相关错误日志
- JumpServer 版本信息
- 网络环境信息

### 2. 问题报告模板

```
**问题描述**：
[详细描述遇到的问题]

**环境信息**：
- JumpServer 版本：
- AMS 版本：
- 操作系统：
- Go 版本：

**重现步骤**：
1. [步骤1]
2. [步骤2]
3. [步骤3]

**错误信息**：
[粘贴完整的错误日志]

**配置信息**：
[相关配置内容，注意隐藏敏感信息]
```

### 3. 联系方式

- 技术支持：[联系方式]
- 文档地址：[文档链接]
- 问题反馈：[反馈渠道]

---

**更新日期**：2025-07-31  
**版本**：v1.0
