# AMS 日志使用指南

## 📋 日志文件结构

AMS 使用分级日志系统，所有日志文件位于 `logs/ams/` 目录下：

```
logs/ams/
├── ALL.log          # 所有等级的日志（最完整）
├── DEBUG.log        # 调试信息
├── INFO.log         # 一般信息日志
├── WARNING.log      # 警告日志
├── ERROR.log        # 错误日志
├── FATAL.log        # 致命错误日志
├── startup.log      # 服务启动日志
└── *.log.2025073114 # 按小时轮转的历史日志
```

## 🔍 日志查看方法

### 1. 实时监控日志

```bash
# 监控所有日志
tail -f logs/ams/ALL.log

# 监控错误日志
tail -f logs/ams/ERROR.log

# 监控信息日志
tail -f logs/ams/INFO.log

# 同时监控多个日志文件
tail -f logs/ams/INFO.log logs/ams/ERROR.log logs/ams/WARNING.log
```

### 2. 查看历史日志

```bash
# 查看最近的日志
tail -100 logs/ams/ALL.log

# 查看特定时间段的日志
grep "2025-07-31 14:" logs/ams/ALL.log

# 搜索特定关键词
grep "JumpServer" logs/ams/INFO.log
grep "error" logs/ams/ERROR.log
```

### 3. 启动问题排查

```bash
# 查看服务启动日志
cat logs/ams/startup.log

# 检查启动是否成功
tail -10 logs/ams/startup.log
```

## 📊 日志等级说明

### DEBUG
- **内容**：详细的调试信息
- **用途**：开发和深度排查问题
- **示例**：函数调用、变量值、执行流程

### INFO
- **内容**：一般的信息性消息
- **用途**：了解系统运行状态
- **示例**：
  ```
  2025-07-31 14:04:29.295007 INFO jumpserver/client.go:73 JumpServer connection test successful
  ```

### WARNING
- **内容**：警告信息，不影响正常运行
- **用途**：关注潜在问题
- **示例**：配置项缺失、性能警告

### ERROR
- **内容**：错误信息，影响功能正常运行
- **用途**：排查功能故障
- **示例**：API 调用失败、数据库连接错误

### FATAL
- **内容**：致命错误，导致程序退出
- **用途**：排查服务无法启动的问题
- **示例**：配置文件错误、端口占用

## 🔧 常见问题排查

### 1. 服务启动失败

```bash
# 1. 查看启动日志
cat logs/ams/startup.log

# 2. 查看致命错误
cat logs/ams/FATAL.log

# 3. 查看最新错误
tail -20 logs/ams/ERROR.log
```

### 2. JumpServer 同步问题

```bash
# 1. 查看 JumpServer 相关日志
grep "JumpServer" logs/ams/ALL.log

# 2. 查看同步错误
grep "sync failed" logs/ams/ERROR.log

# 3. 查看连接状态
grep "connection test" logs/ams/INFO.log
```

### 3. API 请求问题

```bash
# 1. 查看 HTTP 请求日志
grep "HTTP" logs/ams/ALL.log

# 2. 查看认证问题
grep "unauthorized" logs/ams/WARNING.log

# 3. 查看参数错误
grep "parameter" logs/ams/ERROR.log
```

## 📈 日志轮转

### 自动轮转
- **频率**：每小时轮转一次
- **格式**：`日志文件名.YYYYMMDDHH`
- **示例**：`INFO.log.2025073114` (2025年7月31日14时)

### 手动清理
```bash
# 删除7天前的日志
find logs/ams/ -name "*.log.????????" -mtime +7 -delete

# 查看日志文件大小
du -sh logs/ams/*

# 压缩历史日志
gzip logs/ams/*.log.????????
```

## 🚨 监控告警

### 关键日志关键词

**成功状态**：
- `connection test successful` - JumpServer 连接成功
- `sync successful` - 同步成功
- `created successfully` - 创建成功

**错误状态**：
- `failed to create` - 创建失败
- `connection failed` - 连接失败
- `sync failed` - 同步失败
- `API error` - API 错误
- `authentication failed` - 认证失败

### 监控脚本示例

```bash
#!/bin/bash
# 监控错误日志
ERROR_COUNT=$(grep -c "ERROR" logs/ams/ERROR.log)
if [ $ERROR_COUNT -gt 10 ]; then
    echo "警告：错误日志过多 ($ERROR_COUNT 条)"
fi

# 监控 JumpServer 连接
LAST_SUCCESS=$(grep "connection test successful" logs/ams/INFO.log | tail -1)
if [ -z "$LAST_SUCCESS" ]; then
    echo "警告：JumpServer 连接可能有问题"
fi
```

## 💡 最佳实践

### 1. 日常监控
```bash
# 创建监控别名
alias ams-logs='tail -f logs/ams/ALL.log'
alias ams-errors='tail -f logs/ams/ERROR.log'
alias ams-info='tail -f logs/ams/INFO.log'
```

### 2. 问题排查流程
1. **查看启动日志**：确认服务是否正常启动
2. **查看错误日志**：定位具体错误信息
3. **查看信息日志**：了解系统运行状态
4. **搜索关键词**：定位特定功能的问题

### 3. 日志保留策略
- **实时日志**：保留当前运行的日志文件
- **历史日志**：保留最近7天的轮转日志
- **归档日志**：压缩保存重要的历史日志

## 📞 获取支持

如果遇到日志相关问题：

1. **收集信息**：
   ```bash
   # 收集最近的日志
   tar -czf ams-logs-$(date +%Y%m%d).tar.gz logs/ams/
   ```

2. **提供信息**：
   - 问题发生的时间
   - 相关的错误日志
   - 系统配置信息
   - 操作步骤

3. **联系支持**：提供收集的日志文件和问题描述

---

**更新日期**：2025-07-31  
**版本**：v1.0
