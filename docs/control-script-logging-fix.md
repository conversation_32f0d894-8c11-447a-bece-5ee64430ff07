# Control 脚本日志系统修复记录

## 📋 问题描述

在使用 `bash control restart ams` 启动服务后，发现无法在对应等级的日志文件中看到应用程序日志，所有日志都被重定向到了 `stdout.log` 文件中。

### 问题现象

1. **日志文件问题**：
   - `logs/ams/INFO.log`、`logs/ams/ERROR.log` 等文件为空或内容很少
   - 所有应用程序日志都出现在 `logs/ams/stdout.log` 中
   - 无法按日志等级进行问题排查

2. **排查困难**：
   - JumpServer 同步失败时，无法快速定位错误日志
   - 所有日志混在一个文件中，难以筛选
   - 日志等级分类失效

### 错误配置

```bash
# control 脚本中的问题配置
nohup $CWD/$binfile &> logs/${mod}/stdout.log &
```

## 🔍 问题分析

### 1. 重定向覆盖问题

**根本原因**：`control` 脚本使用了 `&>` 重定向，将进程的标准输出和标准错误都重定向到 `stdout.log` 文件。

```bash
# 问题分析
&> logs/${mod}/stdout.log
# 等价于
> logs/${mod}/stdout.log 2>&1
```

这种重定向方式会：
- 覆盖应用程序内部的日志配置
- 将所有输出（包括应用程序日志）都写入 `stdout.log`
- 导致应用程序的分级日志系统失效

### 2. 日志系统冲突

**应用程序日志配置**：
```yaml
# etc/ams.yml
logger:
  dir: logs/ams
  level: INFO
  keep_hours: 168
```

**应用程序日志初始化**：
```go
// src/modules/ams/ams.go:78
loggeri.Init(config.Config.Logger)
```

应用程序本身有完善的日志系统，支持：
- 按等级分文件记录（INFO.log、ERROR.log、WARNING.log 等）
- 按小时自动轮转
- 可配置的日志保留时间

但是 `control` 脚本的重定向覆盖了这些配置。

## ✅ 解决方案

### 1. 修改重定向策略

将 `control` 脚本中的重定向从覆盖所有输出改为只记录启动日志：

```bash
# 修改前：覆盖所有输出
nohup $CWD/$binfile &> logs/${mod}/stdout.log &

# 修改后：只记录启动日志
nohup $CWD/$binfile > logs/${mod}/startup.log 2>&1 &
```

### 2. 分离启动日志和应用日志

- **启动日志**：`logs/ams/startup.log` - 记录服务启动过程中的输出
- **应用日志**：`logs/ams/*.log` - 由应用程序日志系统管理

这样既保留了启动日志的记录，又不影响应用程序的日志系统。

## 🔧 修改的文件

### control 脚本

<details>
<summary>修改详情</summary>

**文件位置**：`control`

**修改内容**：
```bash
# 第45-46行
# 修改前
mkdir -p logs/$mod
nohup $CWD/$binfile &> logs/${mod}/stdout.log &

# 修改后  
mkdir -p logs/$mod
# 启动服务，让应用程序自己处理日志
nohup $CWD/$binfile > logs/${mod}/startup.log 2>&1 &
```

**修改说明**：
- 保留了 `logs` 目录的创建
- 将重定向目标从 `stdout.log` 改为 `startup.log`
- 添加了注释说明用途

</details>

## 📊 修改前后对比

### 日志文件结构

**修改前**：
```
logs/ams/
├── stdout.log          # 所有日志都在这里
├── INFO.log           # 空文件或很少内容
├── ERROR.log          # 空文件
├── WARNING.log        # 空文件
└── ...                # 其他等级日志文件基本为空
```

**修改后**：
```
logs/ams/
├── startup.log        # 服务启动日志
├── ALL.log           # 所有等级日志
├── INFO.log          # 信息日志（JumpServer 连接等）
├── ERROR.log         # 错误日志（同步失败等）
├── WARNING.log       # 警告日志
├── DEBUG.log         # 调试日志
├── FATAL.log         # 致命错误日志
└── *.log.2025073114  # 按小时轮转的历史日志
```

### 功能对比

| 功能 | 修改前 | 修改后 | 改善 |
|------|--------|--------|------|
| 日志分级 | ❌ 失效 | ✅ 正常 | 可按等级查看日志 |
| 问题排查 | ❌ 困难 | ✅ 便捷 | 快速定位错误 |
| 日志轮转 | ❌ 失效 | ✅ 正常 | 自动管理历史日志 |
| 启动日志 | ✅ 有记录 | ✅ 有记录 | 保持原有功能 |
| 应用日志 | ❌ 混乱 | ✅ 清晰 | 按配置正常工作 |

## 🧪 测试验证

### 1. 启动测试

```bash
# 重启服务
bash control restart ams

# 检查启动日志
cat logs/ams/startup.log
```

**预期结果**：
- 服务正常启动
- `startup.log` 包含启动信息
- 应用日志文件开始正常记录

### 2. 日志分级测试

```bash
# 查看不同等级的日志
tail -5 logs/ams/INFO.log      # 应该有 JumpServer 连接日志
tail -5 logs/ams/ERROR.log     # 应该有错误信息（如果有的话）
tail -5 logs/ams/ALL.log       # 应该有所有等级的日志
```

### 3. 功能测试

```bash
# 创建主机，触发 JumpServer 同步
curl -X POST http://localhost:8002/api/ams-ce/host \
  -H "Content-Type: application/json" \
  -H "X-User-Token: your-token" \
  -d '{"ip":"*************","ident":"test-host","name":"测试主机"}'

# 检查同步日志
grep "JumpServer" logs/ams/INFO.log
grep "sync" logs/ams/ALL.log
```

### 4. 验证结果

- ✅ **启动日志正常**：`startup.log` 记录服务启动信息
- ✅ **应用日志分级**：不同等级日志正确记录到对应文件
- ✅ **JumpServer 日志**：连接和同步日志出现在 `INFO.log` 中
- ✅ **日志轮转正常**：按小时自动轮转历史日志

## 📋 使用指南

### 1. 查看启动问题

```bash
# 查看服务启动日志
cat logs/ams/startup.log

# 查看启动失败原因
tail -20 logs/ams/startup.log
```

### 2. 排查运行问题

```bash
# 查看错误日志
tail -f logs/ams/ERROR.log

# 查看警告日志
tail -f logs/ams/WARNING.log

# 查看所有日志
tail -f logs/ams/ALL.log
```

### 3. 监控 JumpServer 同步

```bash
# 查看 JumpServer 相关日志
grep "JumpServer" logs/ams/INFO.log

# 监控同步状态
grep "sync" logs/ams/ALL.log
```

### 4. 实时监控

```bash
# 同时监控多个日志文件
tail -f logs/ams/INFO.log logs/ams/ERROR.log logs/ams/WARNING.log

# 监控所有日志
tail -f logs/ams/ALL.log
```

## 🚀 部署建议

### 1. 升级步骤

1. **备份当前日志**：
   ```bash
   cp -r logs/ams logs/ams.backup.$(date +%Y%m%d)
   ```

2. **更新 control 脚本**：应用上述修改

3. **重启服务**：
   ```bash
   bash control restart ams
   ```

4. **验证日志**：检查各个日志文件是否正常工作

### 2. 监控配置

```bash
# 添加日志监控别名
echo 'alias ams-logs="tail -f logs/ams/ALL.log"' >> ~/.bashrc
echo 'alias ams-errors="tail -f logs/ams/ERROR.log"' >> ~/.bashrc
echo 'alias ams-info="tail -f logs/ams/INFO.log"' >> ~/.bashrc
```

### 3. 日志清理

```bash
# 清理旧的 stdout.log（如果不再需要）
mv logs/ams/stdout.log logs/ams/stdout.log.old

# 设置日志清理计划任务
# 删除7天前的轮转日志
0 2 * * * find /path/to/logs/ams/ -name "*.log.????????" -mtime +7 -delete
```

## 🔄 版本历史

- **v1.0** - 初始版本，所有输出重定向到 `stdout.log`
- **v1.1** - 修复日志系统，分离启动日志和应用日志
- **v1.2** - 优化日志监控和清理机制

## 📝 相关文档

- [日志使用指南](logging-guide.md) - 详细的日志使用说明
- [故障排查指南](jumpserver-troubleshooting.md) - 问题排查方法
- [JumpServer 同步修复](jumpserver-sync-fix.md) - 同步问题修复记录

---

**修复日期**：2025-07-31  
**修复人员**：开发团队  
**影响范围**：日志系统和问题排查  
**风险等级**：低（改善功能，无破坏性变更）
